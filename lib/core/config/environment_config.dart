import 'package:freezed_annotation/freezed_annotation.dart';

part 'environment_config.freezed.dart';

/// Environment-specific configuration that automatically switches between debug and production
/// 
/// This configuration handles environment-specific settings like logging levels,
/// debug flags, and other development vs production differences.
@freezed
class EnvironmentConfig with _$EnvironmentConfig {
  const factory EnvironmentConfig({
    required bool isDebug,
    required bool enableLogging,
    required bool enablePerformanceMonitoring,
    required bool enableCrashReporting,
    required String logLevel,
  }) = _EnvironmentConfig;

  /// Current environment configuration based on build mode
  static EnvironmentConfig get current {
    const isDebug = bool.fromEnvironment('DEBUG', defaultValue: false);
    
    if (isDebug) {
      return const EnvironmentConfig(
        isDebug: true,
        enableLogging: true,
        enablePerformanceMonitoring: true,
        enableCrashReporting: false,
        logLevel: 'DEBUG',
      );
    } else {
      return const EnvironmentConfig(
        isDebug: false,
        enableLogging: false,
        enablePerformanceMonitoring: true,
        enableCrashReporting: true,
        logLevel: 'ERROR',
      );
    }
  }
}
