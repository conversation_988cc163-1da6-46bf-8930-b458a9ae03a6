import 'package:flutter/material.dart';
import '../../dependency_injection/injectable.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import '../flavors/base_flavor_config.dart';

/// Simple, working example of how to use the new configuration system
/// 
/// This example shows practical usage without complex dependencies
/// that might cause compilation issues.

class SimpleConfigurationExample extends StatelessWidget {
  const SimpleConfigurationExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Access configurations through dependency injection
    final flavorConfig = getIt<FlavorConfig>();
    final paymentConfig = getIt<PaymentFeatureConfig>();
    final investmentConfig = getIt<InvestmentFeatureConfig>();
    final uiConfig = getIt<UiFeatureConfig>();

    return Scaffold(
      appBar: AppBar(
        title: Text(flavorConfig.appName),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // App Information
            _buildSection(
              'App Configuration',
              [
                'App Name: ${flavorConfig.appName}',
                'Flavor: ${flavorConfig.flavorName}',
                'Base URL: ${flavorConfig.baseUrl}',
                'Account Type: ${flavorConfig.accountType}',
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Payment Configuration
            _buildSection(
              'Payment Configuration',
              [
                'Use Static Payment Types: ${paymentConfig.useStaticPaymentTypes}',
                'Fetch from API: ${paymentConfig.shouldFetchWalletCoinsFromApi}',
                'Show Add Wallet Address: ${paymentConfig.showAddWalletAddress}',
                'Show Deposit TX Hash: ${paymentConfig.showDepositeTxHash}',
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Investment Configuration
            _buildSection(
              'Investment Configuration',
              [
                'Show Smart Investment: ${investmentConfig.showSmartInvestmentPurchasePercentage}',
                'Use Static Percentages: ${investmentConfig.useStaticPurchasePercentage}',
                'Show Purchase Fields: ${investmentConfig.showPurchaseProductFields}',
                'Show Benefit Rules: ${investmentConfig.showBenefitRules}',
              ],
            ),
            
            const SizedBox(height: 20),
            
            // UI Configuration
            _buildSection(
              'UI Configuration',
              [
                'Show Trading Wallet: ${uiConfig.showTradingWallet}',
                'Show Transfer: ${uiConfig.showTransfer}',
                'Show Mentor VIP Level: ${uiConfig.showMentorVipLevel}',
                'Market Color: ${uiConfig.marketColor.name}',
                'Fetch Community Records: ${uiConfig.fetchCommunityRecords}',
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Conditional UI based on configuration
            if (uiConfig.showTradingWallet)
              const Card(
                child: ListTile(
                  leading: Icon(Icons.account_balance_wallet),
                  title: Text('Trading Wallet'),
                  subtitle: Text('This is shown because showTradingWallet is enabled'),
                ),
              ),
            
            if (paymentConfig.showDepositeTxHash)
              const Card(
                child: ListTile(
                  leading: Icon(Icons.receipt),
                  title: Text('Transaction Hash Field'),
                  subtitle: Text('This is shown because showDepositeTxHash is enabled'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 4),
          child: Text('• $item'),
        )),
      ],
    );
  }
}

/// Example of how to use configuration in business logic
class ConfigurationService {
  final PaymentFeatureConfig _paymentConfig;
  final InvestmentFeatureConfig _investmentConfig;

  ConfigurationService(this._paymentConfig, this._investmentConfig);

  /// Example method that uses payment configuration
  bool shouldShowPaymentOptions() {
    return _paymentConfig.useStaticPaymentTypes || 
           _paymentConfig.shouldFetchWalletCoinsFromApi;
  }

  /// Example method that uses investment configuration
  bool shouldShowInvestmentFeatures() {
    return _investmentConfig.showSmartInvestmentPurchasePercentage;
  }

  /// Example method that combines multiple configurations
  Map<String, dynamic> getFeatureFlags() {
    return {
      'payment': {
        'useStaticTypes': _paymentConfig.useStaticPaymentTypes,
        'fetchFromApi': _paymentConfig.shouldFetchWalletCoinsFromApi,
        'showAddWallet': _paymentConfig.showAddWalletAddress,
      },
      'investment': {
        'showSmartInvestment': _investmentConfig.showSmartInvestmentPurchasePercentage,
        'useStaticPercentages': _investmentConfig.useStaticPurchasePercentage,
        'showBenefitRules': _investmentConfig.showBenefitRules,
      },
    };
  }
}

/// Example of how to create the service with dependency injection
ConfigurationService createConfigurationService() {
  return ConfigurationService(
    getIt<PaymentFeatureConfig>(),
    getIt<InvestmentFeatureConfig>(),
  );
}
