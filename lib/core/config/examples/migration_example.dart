/// Example showing how to migrate from old AppConfig to new configuration system
/// 
/// This file demonstrates a before/after comparison of migrating a typical
/// screen that uses configuration settings.

// ============================================================================
// BEFORE: Using Old AppConfig Singleton Pattern
// ============================================================================

/*
class OldDepositScreen extends StatefulWidget {
  @override
  _OldDepositScreenState createState() => _OldDepositScreenState();
}

class _OldDepositScreenState extends State<OldDepositScreen> {
  @override
  void initState() {
    super.initState();
    _initialFunction();
  }

  void _initialFunction() {
    final cubit = context.read<DepositCubit>();
    
    // OLD: Direct singleton access - hard to test
    if (AppConfig.shouldFetchWalletCoinsFromApi) {
      cubit.fetchWalletCoins();
    }
    
    // OLD: Tightly coupled to AppConfig
    final defaultPaymentType = AppConfig.getDefaultPaymentType();
    if (defaultPaymentType != null) {
      cubit.setPaymentType(defaultPaymentType);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Deposit')),
      body: BlocBuilder<DepositCubit, DepositState>(
        builder: (context, state) {
          return Column(
            children: [
              // OLD: Direct feature flag access
              if (AppConfig.instance.showDepositeTxHash)
                _buildTxHashField(),
              
              _buildPaymentTypeSelector(state),
              _buildDepositForm(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPaymentTypeSelector(DepositState state) {
    // OLD: Static method call with global state
    final availableTypes = AppConfig.getAvailablePaymentTypes(
      apiWalletCoins: state.walletCoins,
    );
    
    return DropdownButton<PaymentTypeModel>(
      value: state.selectedPaymentType,
      items: availableTypes.map((type) => DropdownMenuItem(
        value: type,
        child: Text(type.name),
      )).toList(),
      onChanged: (value) {
        if (value != null) {
          context.read<DepositCubit>().setPaymentType(value);
        }
      },
    );
  }

  Widget _buildTxHashField() {
    return TextField(
      decoration: InputDecoration(
        labelText: 'Transaction Hash',
        hintText: 'Enter transaction hash',
      ),
    );
  }

  Widget _buildDepositForm(DepositState state) {
    return Container(
      child: Text('Deposit form content'),
    );
  }
}
*/

// ============================================================================
// AFTER: Using New Configuration System with Dependency Injection
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../dependency_injection/injectable.dart';
import '../../models/app_config/payment_type_model.dart';
import '../feature_configs/payment_feature_config.dart';

// Mock classes for example (these would be real in your app)
class DepositCubit {}
class DepositState {
  final List<dynamic> walletCoins;
  final PaymentTypeModel? selectedPaymentType;
  
  const DepositState({
    this.walletCoins = const [],
    this.selectedPaymentType,
  });
}

class NewDepositScreen extends StatefulWidget {
  const NewDepositScreen({super.key});

  @override
  State<NewDepositScreen> createState() => _NewDepositScreenState();
}

class _NewDepositScreenState extends State<NewDepositScreen> {
  // NEW: Inject configurations instead of using singletons
  late final PaymentFeatureConfig _paymentConfig;

  @override
  void initState() {
    super.initState();
    
    // NEW: Get configuration through dependency injection
    _paymentConfig = getIt<PaymentFeatureConfig>();
    
    _initialFunction();
  }

  void _initialFunction() {
    final cubit = context.read<DepositCubit>();
    
    // NEW: Use injected configuration - easy to mock in tests
    if (_paymentConfig.shouldFetchWalletCoinsFromApi) {
      // cubit.fetchWalletCoins();
    }
    
    // NEW: Clean method call on configuration object
    final defaultPaymentType = _paymentConfig.getDefaultPaymentType();
    if (defaultPaymentType != null) {
      // cubit.setPaymentType(defaultPaymentType);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Deposit')),
      body: BlocBuilder<DepositCubit, DepositState>(
        builder: (context, state) {
          return Column(
            children: [
              // NEW: Clean feature flag access through configuration
              if (_paymentConfig.showDepositeTxHash)
                _buildTxHashField(),
              
              _buildPaymentTypeSelector(state),
              _buildDepositForm(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPaymentTypeSelector(DepositState state) {
    // NEW: Method call on configuration object - much cleaner
    final availableTypes = _paymentConfig.getAvailablePaymentTypes(
      apiWalletCoins: state.walletCoins,
    );
    
    return DropdownButton<PaymentTypeModel>(
      value: state.selectedPaymentType,
      items: availableTypes.map((type) => DropdownMenuItem(
        value: type,
        child: Text(type.name),
      )).toList(),
      onChanged: (value) {
        if (value != null) {
          // context.read<DepositCubit>().setPaymentType(value);
        }
      },
    );
  }

  Widget _buildTxHashField() {
    return const TextField(
      decoration: InputDecoration(
        labelText: 'Transaction Hash',
        hintText: 'Enter transaction hash',
      ),
    );
  }

  Widget _buildDepositForm(DepositState state) {
    return const Text('Deposit form content');
  }
}

// ============================================================================
// TESTING: Comparison of Testing Approaches
// ============================================================================

/*
// OLD: Difficult to test due to singleton dependencies
testWidgets('old deposit screen shows tx hash field', (tester) async {
  // PROBLEM: Can't easily mock AppConfig.instance
  // PROBLEM: Test depends on global state
  // PROBLEM: Hard to test different configurations
  
  await tester.pumpWidget(MaterialApp(
    home: OldDepositScreen(),
  ));
  
  // This test is brittle and depends on actual AppConfig state
  if (AppConfig.instance.showDepositeTxHash) {
    expect(find.byType(TextField), findsOneWidget);
  }
});
*/

// NEW: Easy to test with dependency injection
/*
testWidgets('new deposit screen shows tx hash field when enabled', (tester) async {
  // SOLUTION: Easy to mock configuration
  final mockPaymentConfig = MockPaymentFeatureConfig();
  when(mockPaymentConfig.showDepositeTxHash).thenReturn(true);
  
  // SOLUTION: Register mock in DI container
  getIt.registerSingleton<PaymentFeatureConfig>(mockPaymentConfig);
  
  await tester.pumpWidget(MaterialApp(
    home: NewDepositScreen(),
  ));
  
  // SOLUTION: Predictable test behavior
  expect(find.byType(TextField), findsOneWidget);
  
  // Cleanup
  getIt.reset();
});

testWidgets('new deposit screen hides tx hash field when disabled', (tester) async {
  // SOLUTION: Easy to test different configurations
  final mockPaymentConfig = MockPaymentFeatureConfig();
  when(mockPaymentConfig.showDepositeTxHash).thenReturn(false);
  
  getIt.registerSingleton<PaymentFeatureConfig>(mockPaymentConfig);
  
  await tester.pumpWidget(MaterialApp(
    home: NewDepositScreen(),
  ));
  
  expect(find.byType(TextField), findsNothing);
  
  getIt.reset();
});
*/

// ============================================================================
// BENEFITS SUMMARY
// ============================================================================

/*
Benefits of New Approach:

1. **Testability**
   - Easy to mock configurations
   - Predictable test behavior
   - No global state dependencies

2. **Maintainability**
   - Clear separation of concerns
   - Configuration logic isolated
   - Easy to modify behavior

3. **Flexibility**
   - Easy to add new configurations
   - Runtime configuration possible
   - Feature flag management

4. **Type Safety**
   - Compile-time configuration validation
   - No runtime configuration errors
   - Better IDE support

5. **Performance**
   - No singleton access overhead
   - Configurations cached in DI container
   - Lazy loading possible

Migration Steps:
1. Replace AppConfig.instance calls with getIt<ConfigType>()
2. Replace static method calls with instance method calls
3. Update tests to use mock configurations
4. Remove AppConfig dependencies gradually
*/
