import 'package:flutter/material.dart';
import '../../dependency_injection/injectable.dart';
import '../environment_config.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import '../flavors/base_flavor_config.dart';

/// Test screen to verify all flavor configurations are working correctly
/// 
/// This screen displays the current flavor configuration and allows you to
/// verify that the correct settings are loaded for each flavor.
/// 
/// Usage:
/// 1. Add this screen to your app routes
/// 2. Build with different flavors using --dart-define=FLAVOR=xxx
/// 3. Navigate to this screen to verify configuration
class FlavorTestScreen extends StatelessWidget {
  const FlavorTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final environmentConfig = getIt<EnvironmentConfig>();
    final flavorConfig = getIt<FlavorConfig>();
    final paymentConfig = getIt<PaymentFeatureConfig>();
    final investmentConfig = getIt<InvestmentFeatureConfig>();
    final uiConfig = getIt<UiFeatureConfig>();

    return Scaffold(
      appBar: AppBar(
        title: Text('Flavor Test: ${flavorConfig.flavorName.toUpperCase()}'),
        backgroundColor: _getFlavorColor(flavorConfig.flavorName),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Environment Info
          _buildSection(
            'Environment Configuration',
            [
              _buildInfoTile('Debug Mode', environmentConfig.isDebug.toString()),
              _buildInfoTile('Logging Enabled', environmentConfig.enableLogging.toString()),
              _buildInfoTile('Log Level', environmentConfig.logLevel),
            ],
          ),

          const SizedBox(height: 20),

          // App Info
          _buildSection(
            'App Configuration',
            [
              _buildInfoTile('App Name', flavorConfig.appName),
              _buildInfoTile('Flavor', flavorConfig.flavorName),
              _buildInfoTile('Base URL', flavorConfig.baseUrl),
              _buildInfoTile('WebSocket URL', flavorConfig.marketWsUrl),
              _buildInfoTile('App URL', flavorConfig.appUrl),
              _buildInfoTile('Account Type', flavorConfig.accountType),
              _buildInfoTile('Default Locale', flavorConfig.defaultLocale.toString()),
              _buildInfoTile('Icon Path', flavorConfig.iconPath),
              _buildInfoTile('Intro Video', flavorConfig.introVideoPath),
            ],
          ),

          const SizedBox(height: 20),

          // Payment Configuration
          _buildSection(
            'Payment Configuration',
            [
              _buildInfoTile('Use Static Payment Types', paymentConfig.useStaticPaymentTypes.toString()),
              _buildInfoTile('Fetch from API', paymentConfig.shouldFetchWalletCoinsFromApi.toString()),
              _buildInfoTile('Show Add Wallet Address', paymentConfig.showAddWalletAddress.toString()),
              _buildInfoTile('Show Deposit TX Hash', paymentConfig.showDepositeTxHash.toString()),
              _buildInfoTile('Available Payment Types', paymentConfig.staticPaymentTypes.length.toString()),
            ],
          ),

          const SizedBox(height: 20),

          // Investment Configuration
          _buildSection(
            'Investment Configuration',
            [
              _buildInfoTile('Show Smart Investment', investmentConfig.showSmartInvestmentPurchasePercentage.toString()),
              _buildInfoTile('Use Static Percentages', investmentConfig.useStaticPurchasePercentage.toString()),
              _buildInfoTile('Show Purchase Fields', investmentConfig.showPurchaseProductFields.toString()),
              _buildInfoTile('Show Benefit Rules', investmentConfig.showBenefitRules.toString()),
              _buildInfoTile('Static Percentages Count', investmentConfig.staticPurchasePercentages.length.toString()),
            ],
          ),

          const SizedBox(height: 20),

          // UI Configuration
          _buildSection(
            'UI Configuration',
            [
              _buildInfoTile('Show Debug Version Tag', uiConfig.showDebugVersionTag.toString()),
              _buildInfoTile('Show Trading Wallet', uiConfig.showTradingWallet.toString()),
              _buildInfoTile('Show Transfer Preview', uiConfig.showTransferPreview.toString()),
              _buildInfoTile('Show Transfer', uiConfig.showTransfer.toString()),
              _buildInfoTile('Show Mentor VIP Level', uiConfig.showMentorVipLevel.toString()),
              _buildInfoTile('Show Withdraw History', uiConfig.showWithdrawHistory.toString()),
              _buildInfoTile('Show App Update', uiConfig.showAppUpdate.toString()),
              _buildInfoTile('Market Color', uiConfig.marketColor.name),
              _buildInfoTile('Disable Upper Case Password', uiConfig.disableUpperCasePasswordProtection.toString()),
              _buildInfoTile('Fetch Community Records', uiConfig.fetchCommunityRecords.toString()),
            ],
          ),

          const SizedBox(height: 20),

          // Feature Summary
          _buildSection(
            'Feature Summary',
            [
              _buildFeatureTile('Trading Wallet', uiConfig.showTradingWallet),
              _buildFeatureTile('Transfer Features', uiConfig.showTransfer),
              _buildFeatureTile('Smart Investment', investmentConfig.showSmartInvestmentPurchasePercentage),
              _buildFeatureTile('API Payments', paymentConfig.shouldFetchWalletCoinsFromApi),
              _buildFeatureTile('Benefit Rules', investmentConfig.showBenefitRules),
              _buildFeatureTile('Mentor VIP', uiConfig.showMentorVipLevel),
              _buildFeatureTile('Withdraw History', uiConfig.showWithdrawHistory),
              _buildFeatureTile('App Updates', uiConfig.showAppUpdate),
            ],
          ),

          const SizedBox(height: 40),

          // Build Instructions
          _buildSection(
            'Build Commands for Testing',
            [
              _buildCommandTile('Super Future', 'flutter run --dart-define=FLAVOR=sf'),
              _buildCommandTile('SIS', 'flutter run --dart-define=FLAVOR=sis'),
              _buildCommandTile('CFROEX', 'flutter run --dart-define=FLAVOR=cfroex'),
              _buildCommandTile('NCM', 'flutter run --dart-define=FLAVOR=ncm'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureTile(String feature, bool enabled) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            enabled ? Icons.check_circle : Icons.cancel,
            color: enabled ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(feature),
        ],
      ),
    );
  }

  Widget _buildCommandTile(String flavor, String command) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            flavor,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 2),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              command,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getFlavorColor(String flavorName) {
    switch (flavorName.toLowerCase()) {
      case 'sf':
      case 'sf_app':
        return Colors.blue;
      case 'sis':
        return Colors.green;
      case 'cfroex':
        return Colors.orange;
      case 'ncm':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
