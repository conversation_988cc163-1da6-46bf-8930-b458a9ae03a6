import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../features/smart_investment/domain/models/order_rate/order_rate.dart';
import '../../../features/smart_investment/domain/models/purchase_percentage/purchase_percentage.dart';
import '../../../features/wallet/deposit/domain/models/wallet_coin/wallet_coin.dart';
import '../../dependency_injection/injectable.dart';
import '../../models/app_config/payment_type_model.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import '../flavors/base_flavor_config.dart';

/// Example usage of the new configuration system
/// 
/// This file demonstrates how to use the new modular configuration
/// system in various scenarios throughout the app.

// ============================================================================
// Example 1: Using Configuration in Widgets
// ============================================================================

class ExampleDepositScreen extends StatelessWidget {
  const ExampleDepositScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final paymentConfig = getIt<PaymentFeatureConfig>();
    final uiConfig = getIt<UiFeatureConfig>();
    
    return Scaffold(
      appBar: AppBar(title: const Text('Deposit')),
      body: Column(
        children: [
          // Show deposit TX hash field based on configuration
          if (paymentConfig.showDepositeTxHash)
            const TextField(
              decoration: InputDecoration(labelText: 'Transaction Hash'),
            ),
          
          // Show trading wallet based on UI configuration
          if (uiConfig.showTradingWallet)
            const Card(
              child: ListTile(
                title: Text('Trading Wallet'),
                subtitle: Text('Manage your trading funds'),
              ),
            ),
          
          // Payment type selector
          _buildPaymentTypeSelector(paymentConfig),
        ],
      ),
    );
  }

  Widget _buildPaymentTypeSelector(PaymentFeatureConfig config) {
    // This would typically come from a BLoC state
    const List<WalletCoinData> apiCoins = [];
    
    final availableTypes = config.getAvailablePaymentTypes(
      apiWalletCoins: apiCoins,
    );
    
    return DropdownButton<PaymentTypeModel>(
      items: availableTypes.map((type) => DropdownMenuItem(
        value: type,
        child: Text(type.name),
      )).toList(),
      onChanged: (value) {
        // Handle payment type selection
      },
    );
  }
}

// ============================================================================
// Example 2: Using Configuration in BLoC/Cubit
// ============================================================================

class ExampleSmartInvestmentCubit extends Cubit<ExampleSmartInvestmentState> {
  final InvestmentFeatureConfig _investmentConfig;
  final PaymentFeatureConfig _paymentConfig;

  ExampleSmartInvestmentCubit(
    this._investmentConfig,
    this._paymentConfig,
  ) : super(const ExampleSmartInvestmentState());

  /// Initialize purchase percentages based on configuration
  void initializePurchasePercentages({OrderRateData? apiData}) {
    if (!_investmentConfig.shouldShowPurchasePercentage(apiConfigData: apiData)) {
      return; // Feature disabled
    }

    final percentages = _investmentConfig.getPurchasePercentageList(
      apiData: apiData,
    );

    final defaultPercentage = _investmentConfig.getDefaultPurchasePercentage(
      apiData: apiData,
    );

    emit(state.copyWith(
      purchasePercentages: percentages,
      selectedPercentage: defaultPercentage,
    ));
  }

  /// Set purchase percentage with validation
  void setPurchasePercentage(String percentage, {OrderRateData? apiData}) {
    if (!_investmentConfig.isPurchasePercentageEnabled(
      percentage,
      apiData: apiData,
    )) {
      return; // Invalid percentage
    }

    final updatedPercentages = state.purchasePercentages.map((p) =>
        p.copyWith(isSelected: p.percentage == percentage)).toList();

    emit(state.copyWith(
      purchasePercentages: updatedPercentages,
      selectedPercentage: percentage,
    ));
  }

  /// Check if API data should be fetched
  bool shouldFetchApiData() {
    return _investmentConfig.shouldFetchPurchasePercentageFromApi;
  }
}

// ============================================================================
// Example 3: Configuration-Based Feature Flags
// ============================================================================

class ExampleFeatureFlagWidget extends StatelessWidget {
  const ExampleFeatureFlagWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final uiConfig = getIt<UiFeatureConfig>();
    final investmentConfig = getIt<InvestmentFeatureConfig>();
    final paymentConfig = getIt<PaymentFeatureConfig>();

    return Column(
      children: [
        // Conditional UI based on feature flags
        if (uiConfig.showTradingWallet)
          const ListTile(
            title: Text('Trading Wallet'),
            leading: Icon(Icons.account_balance_wallet),
          ),

        if (uiConfig.showTransfer)
          const ListTile(
            title: Text('Transfer Funds'),
            leading: Icon(Icons.swap_horiz),
          ),

        if (investmentConfig.showBenefitRules)
          const ListTile(
            title: Text('Benefit Rules'),
            leading: Icon(Icons.rule),
          ),

        if (paymentConfig.showAddWalletAddress)
          const ListTile(
            title: Text('Add Wallet Address'),
            leading: Icon(Icons.add),
          ),

        // Market color configuration
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getMarketColor(uiConfig),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Text('Market Indicator'),
        ),
      ],
    );
  }

  Color _getMarketColor(UiFeatureConfig config) {
    switch (config.marketColor) {
      case MarketColor.greenUpRedDown:
        return Colors.green;
      case MarketColor.redUpGreenDown:
        return Colors.red;
    }
  }
}

// ============================================================================
// Example 4: Testing with Mock Configurations
// ============================================================================

class MockPaymentFeatureConfig extends PaymentFeatureConfig {
  const MockPaymentFeatureConfig({
    super.useStaticPaymentTypes = true,
    super.shouldFetchWalletCoinsFromApi = false,
    super.showAddWalletAddress = false,
    super.showDepositeTxHash = false,
    super.staticPaymentTypes = const [],
    super.staticWithdrawAddressTypes = const [],
  });
}

class MockInvestmentFeatureConfig extends InvestmentFeatureConfig {
  const MockInvestmentFeatureConfig({
    super.showSmartInvestmentPurchasePercentage = true,
    super.useStaticPurchasePercentage = true,
    super.showPurchaseProductFields = false,
    super.showBenefitRules = false,
    super.staticPurchasePercentages = const [],
  });
}

// ============================================================================
// Example 5: App Initialization with Configuration
// ============================================================================

class ExampleAppInitializer {
  static Future<void> initialize() async {
    // Initialize dependency injection
    // This would be called in main.dart
    
    // Access configurations after DI setup
    final flavorConfig = getIt<FlavorConfig>();
    final environmentConfig = getIt<EnvironmentConfig>();
    
    print('Initializing ${flavorConfig.appName}');
    print('Debug mode: ${environmentConfig.isDebug}');
    print('Base URL: ${flavorConfig.baseUrl}');
    
    // Configure app based on flavor
    await _configureApp(flavorConfig);
  }

  static Future<void> _configureApp(FlavorConfig config) async {
    // App-specific initialization based on configuration
    // This could include setting up analytics, crash reporting, etc.
  }
}

// ============================================================================
// Supporting State Class
// ============================================================================

class ExampleSmartInvestmentState {
  final List<PurchasePercentage> purchasePercentages;
  final String selectedPercentage;

  const ExampleSmartInvestmentState({
    this.purchasePercentages = const [],
    this.selectedPercentage = '100',
  });

  ExampleSmartInvestmentState copyWith({
    List<PurchasePercentage>? purchasePercentages,
    String? selectedPercentage,
  }) {
    return ExampleSmartInvestmentState(
      purchasePercentages: purchasePercentages ?? this.purchasePercentages,
      selectedPercentage: selectedPercentage ?? this.selectedPercentage,
    );
  }
}
