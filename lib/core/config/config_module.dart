import 'package:injectable/injectable.dart';
import 'environment_config.dart';
import 'feature_configs/investment_feature_config.dart';
import 'feature_configs/payment_feature_config.dart';
import 'feature_configs/ui_feature_config.dart';
import 'flavors/base_flavor_config.dart';
import 'flavors/cfroex_flavor_config.dart';
import 'flavors/ncm_flavor_config.dart';
import 'flavors/sf_flavor_config.dart';
import 'flavors/sis_flavor_config.dart';

/// Central dependency injection module for all configuration providers
/// 
/// This module registers all configuration-related dependencies and ensures
/// they are available throughout the app via dependency injection.
@module
abstract class ConfigModule {
  /// Environment configuration (debug/production settings)
  @singleton
  EnvironmentConfig get environmentConfig => EnvironmentConfig.current;

  /// Main flavor configuration - dynamic based on build-time flavor selection
  @singleton
  FlavorConfig get flavorConfig {
    const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'sf');
    final isDebug = environmentConfig.isDebug;

    switch (flavor.toLowerCase()) {
      case 'sis':
        return SisFlavorConfig.create(isDebug);
      case 'cfroex':
        return CfroexFlavorConfig.create(isDebug);
      case 'ncm':
        return NcmFlavorConfig.create(isDebug);
      case 'sf':
      case 'sf_app':
      default:
        return SfFlavorConfig.create(isDebug);
    }
  }

  /// Payment feature configuration derived from flavor config
  @singleton
  PaymentFeatureConfig get paymentFeatureConfig => flavorConfig.paymentConfig;

  /// Investment feature configuration derived from flavor config
  @singleton
  InvestmentFeatureConfig get investmentFeatureConfig => flavorConfig.investmentConfig;

  /// UI feature configuration derived from flavor config
  @singleton
  UiFeatureConfig get uiFeatureConfig => flavorConfig.uiConfig;

  /// Debug flag for easy access throughout the app
  @Named('isDebug')
  @singleton
  bool get isDebug => environmentConfig.isDebug;
}

/// Dynamic flavor selection is now implemented!
///
/// Use build commands to select different flavors:
///
/// ```bash
/// # Super Future flavor (default)
/// flutter run --dart-define=FLAVOR=sf
///
/// # SIS flavor
/// flutter run --dart-define=FLAVOR=sis
///
/// # CFROEX flavor
/// flutter run --dart-define=FLAVOR=cfroex
///
/// # NCM flavor
/// flutter run --dart-define=FLAVOR=ncm
/// ```
///
/// Each flavor has its own configuration:
/// - **SF**: Static payments, standard UI, English
/// - **SIS**: API payments, minimal UI, English
/// - **CFROEX**: Static payments, enhanced UI, Spanish
/// - **NCM**: Static payments, standard UI, English
