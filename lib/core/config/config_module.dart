import 'package:injectable/injectable.dart';
import 'environment_config.dart';
import 'feature_configs/investment_feature_config.dart';
import 'feature_configs/payment_feature_config.dart';
import 'feature_configs/ui_feature_config.dart';
import 'flavors/base_flavor_config.dart';
import 'flavors/sf_flavor_config.dart';

/// Central dependency injection module for all configuration providers
/// 
/// This module registers all configuration-related dependencies and ensures
/// they are available throughout the app via dependency injection.
@module
abstract class ConfigModule {
  /// Environment configuration (debug/production settings)
  @singleton
  EnvironmentConfig get environmentConfig => EnvironmentConfig.current;

  /// Main flavor configuration - currently hardcoded to Super Future
  /// TODO: Make this dynamic based on build-time flavor selection
  @singleton
  FlavorConfig get flavorConfig => SfFlavorConfig.create();

  /// Payment feature configuration derived from flavor config
  @singleton
  PaymentFeatureConfig get paymentFeatureConfig => flavorConfig.paymentConfig;

  /// Investment feature configuration derived from flavor config
  @singleton
  InvestmentFeatureConfig get investmentFeatureConfig => flavorConfig.investmentConfig;

  /// UI feature configuration derived from flavor config
  @singleton
  UiFeatureConfig get uiFeatureConfig => flavorConfig.uiConfig;

  /// Debug flag for easy access throughout the app
  @Named('isDebug')
  @singleton
  bool get isDebug => environmentConfig.isDebug;
}

/// Future enhancement: Dynamic flavor selection
/// 
/// To make flavor selection dynamic, replace the hardcoded flavorConfig
/// getter with this implementation:
/// 
/// ```dart
/// @singleton
/// FlavorConfig get flavorConfig {
///   const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'sf');
///   
///   switch (flavor) {
///     case 'sis':
///       return SisFlavorConfig.create();
///     case 'cfroex':
///       return CfroexFlavorConfig.create();
///     case 'ncm':
///       return NcmFlavorConfig.create();
///     case 'sf':
///     default:
///       return SfFlavorConfig.create();
///   }
/// }
/// ```
/// 
/// Then use build commands like:
/// `flutter run --dart-define=FLAVOR=sis`
