import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import '../../constants/enums.dart';
import '../../../flavors.dart';
import 'base_app_configuration.dart';

/// Super Future app flavor configuration
/// 
/// This class provides configuration specific to the Super Future flavor,
/// extending the base configuration with SF-specific settings.
@Named('sf_app')
@singleton
class SfAppConfiguration extends BaseAppConfiguration {
  SfAppConfiguration(@Named('isDebug') bool isDebug)
      : super(
          flavor: Flavor.sf_app,
          appName: 'Super Future',
          baseUrl: isDebug
              ? 'https://api.superfuture.world'
              : 'https://api.superfuture.world',
          marketWsUrl: isDebug
              ? 'wss://api.superfuture.world/ws'
              : 'wss://api.superfuture.world/ws',
          appUrl: 'https://superfuture.world/',
          icon: 'assets/logo/sf_app/logo.svg',
          introVideo: 'assets/splash/sf_app/introVideo.mp4',
          accountType: '3',
          defaultLocale: const Locale('en', 'US'),
          marketColor: MarketColor.greenUpRedDown,
          showDebugVersionTag: true,
          showTradingWallet: true,
          showTransferPreview: true,
          showBenefitRules: false,
          showTransfer: true,
          showPurchaseProductFields: false,
          showMentorVipLevel: false,
          showWithdrawHistory: false,
          showAppUpdate: false,
          showAddWalletAddress: false,
          showDepositeTxHash: false,
          fetchCommunityRecords: true,
          disableUpperCasePasswordProtection: false,
          showSmartInvestmentPurchasePercentage: true,
          showStaticPaymentTypes: true,
          showStaticPurchasePercentage: true,
        );
}
