# Configuration System Integration Guide

## Quick Start

This guide shows you how to integrate the new configuration system into your existing Flutter app with minimal changes.

## Step 1: Update Dependency Injection

### 1.1 Add ConfigModule to Injectable

First, ensure the `ConfigModule` is included in your dependency injection setup:

```dart
// In lib/core/dependency_injection/injectable.dart
import '../config/config_module.dart';

// The ConfigModule will be automatically registered when you run:
// flutter packages pub run build_runner build
```

### 1.2 Regenerate Injectable Configuration

```bash
cd /path/to/your/project
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## Step 2: Replace AppConfig Usage (Gradual Migration)

### 2.1 Payment Configuration Migration

**Before:**
```dart
// Old way - using AppConfig singleton
final paymentTypes = AppConfig.getAvailablePaymentTypes(apiWalletCoins: coins);
final shouldFetch = AppConfig.shouldFetchWalletCoinsFromApi;
```

**After:**
```dart
// New way - using dependency injection
final paymentConfig = getIt<PaymentFeatureConfig>();
final paymentTypes = paymentConfig.getAvailablePaymentTypes(apiWalletCoins: coins);
final shouldFetch = paymentConfig.shouldFetchWalletCoinsFromApi;
```

### 2.2 Investment Configuration Migration

**Before:**
```dart
// Old way
final percentages = AppConfig.getList(apiData: data);
final shouldShow = AppConfig.shouldShowSmartInvestmentPurchasePercentage();
```

**After:**
```dart
// New way
final investmentConfig = getIt<InvestmentFeatureConfig>();
final percentages = investmentConfig.getPurchasePercentageList(apiData: data);
final shouldShow = investmentConfig.shouldShowPurchasePercentage();
```

### 2.3 UI Configuration Migration

**Before:**
```dart
// Old way
final showWallet = AppConfig.instance.showTradingWallet;
final marketColor = AppConfig.instance.marketColor;
```

**After:**
```dart
// New way
final uiConfig = getIt<UiFeatureConfig>();
final showWallet = uiConfig.showTradingWallet;
final marketColor = uiConfig.marketColor;
```

## Step 3: Update Specific Files

### 3.1 Update SmartInvestmentCubit

**File:** `lib/features/smart_investment/logic/smart_investment/smart_investment_cubit.dart`

**Find this method:**
```dart
void _updatePurchasePercentagesFromConfig() {
  final updatedPercentages = AppConfig.getList(
    apiData: state.purchasePercentageConfigData,
  );
  // ...
}
```

**Replace with:**
```dart
void _updatePurchasePercentagesFromConfig() {
  final investmentConfig = getIt<InvestmentFeatureConfig>();
  final updatedPercentages = investmentConfig.getPurchasePercentageList(
    apiData: state.purchasePercentageConfigData,
  );
  // ...
}
```

### 3.2 Update DepositScreen

**File:** `lib/features/wallet/deposit/screens/deposit_screen.dart`

**Find this method:**
```dart
List<PaymentTypeModel> _buildCurrencyItems(DepositState state) {
  final availablePaymentTypes = AppConfig.getAvailablePaymentTypes(
    apiWalletCoins: state.walletCoins,
  );
  return availablePaymentTypes;
}
```

**Replace with:**
```dart
List<PaymentTypeModel> _buildCurrencyItems(DepositState state) {
  final paymentConfig = getIt<PaymentFeatureConfig>();
  final availablePaymentTypes = paymentConfig.getAvailablePaymentTypes(
    apiWalletCoins: state.walletCoins,
  );
  return availablePaymentTypes;
}
```

### 3.3 Update MentorProfileScreen

**File:** `lib/features/smart_investment/screens/mentor_profile_screen.dart`

**Find this code:**
```dart
if (AppConfig.instance.showSmartInvestmentPurchasePercentage) {
  cubit.getPurchasePercentageConfig();
}
```

**Replace with:**
```dart
final investmentConfig = getIt<InvestmentFeatureConfig>();
if (investmentConfig.showSmartInvestmentPurchasePercentage) {
  cubit.getPurchasePercentageConfig();
}
```

## Step 4: Test the Integration

### 4.1 Create a Test Screen

Add this test screen to verify the configuration is working:

```dart
// lib/core/config/test_config_screen.dart
import 'package:flutter/material.dart';
import '../dependency_injection/injectable.dart';
import 'feature_configs/payment_feature_config.dart';
import 'flavors/base_flavor_config.dart';

class TestConfigScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final flavorConfig = getIt<FlavorConfig>();
    final paymentConfig = getIt<PaymentFeatureConfig>();
    
    return Scaffold(
      appBar: AppBar(title: Text('Config Test')),
      body: Column(
        children: [
          Text('App: ${flavorConfig.appName}'),
          Text('Flavor: ${flavorConfig.flavorName}'),
          Text('Static Payments: ${paymentConfig.useStaticPaymentTypes}'),
        ],
      ),
    );
  }
}
```

### 4.2 Add Route to Test Screen

```dart
// In your route generator, add:
case '/test-config':
  return MaterialPageRoute(builder: (_) => TestConfigScreen());
```

### 4.3 Navigate to Test Screen

```dart
// From any screen:
Navigator.pushNamed(context, '/test-config');
```

## Step 5: Verify Everything Works

### 5.1 Check App Startup

1. Run the app: `flutter run`
2. Verify no dependency injection errors
3. Check that the app starts normally

### 5.2 Test Configuration Access

1. Navigate to the test screen
2. Verify configuration values are displayed correctly
3. Check that feature flags work as expected

### 5.3 Test Existing Features

1. Test deposit screen (payment types)
2. Test smart investment screen (purchase percentages)
3. Verify UI features show/hide correctly

## Step 6: Gradual Migration Strategy

### Phase 1: Core Configurations (Week 1)
- [ ] Update payment-related screens
- [ ] Update investment-related screens
- [ ] Test thoroughly

### Phase 2: UI Configurations (Week 2)
- [ ] Update feature flag usage
- [ ] Update conditional UI rendering
- [ ] Test all flavors

### Phase 3: Cleanup (Week 3)
- [ ] Remove old AppConfig usage
- [ ] Update tests to use new system
- [ ] Documentation updates

## Troubleshooting

### Common Issues

1. **Dependency Injection Errors**
   ```
   Error: No registered instance found for type FlavorConfig
   ```
   **Solution:** Run `flutter packages pub run build_runner build`

2. **Import Errors**
   ```
   Error: Target of URI doesn't exist
   ```
   **Solution:** Check import paths and ensure files exist

3. **Configuration Not Found**
   ```
   Error: getIt<ConfigType>() returns null
   ```
   **Solution:** Ensure ConfigModule is properly registered

### Debug Tips

```dart
// Add this to debug configuration issues
void debugConfiguration() {
  try {
    final flavorConfig = getIt<FlavorConfig>();
    print('✅ FlavorConfig loaded: ${flavorConfig.appName}');
    
    final paymentConfig = getIt<PaymentFeatureConfig>();
    print('✅ PaymentConfig loaded: ${paymentConfig.useStaticPaymentTypes}');
    
    final investmentConfig = getIt<InvestmentFeatureConfig>();
    print('✅ InvestmentConfig loaded: ${investmentConfig.showSmartInvestmentPurchasePercentage}');
    
  } catch (e) {
    print('❌ Configuration error: $e');
  }
}
```

## Next Steps

1. **Complete Migration:** Follow the phase plan above
2. **Add New Flavors:** Use the flavor creation guide
3. **Enhance Testing:** Implement configuration mocking
4. **Monitor Performance:** Check for any performance impacts

## Support

If you encounter issues during integration:

1. Check the error logs for specific dependency injection errors
2. Verify all imports are correct
3. Ensure build_runner has been executed
4. Test with the simple example screen first

The new configuration system is designed to be backward compatible, so you can migrate gradually without breaking existing functionality.
