// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'environment_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EnvironmentConfig {
  bool get isDebug => throw _privateConstructorUsedError;
  bool get enableLogging => throw _privateConstructorUsedError;
  bool get enablePerformanceMonitoring => throw _privateConstructorUsedError;
  bool get enableCrashReporting => throw _privateConstructorUsedError;
  String get logLevel => throw _privateConstructorUsedError;

  /// Create a copy of EnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EnvironmentConfigCopyWith<EnvironmentConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnvironmentConfigCopyWith<$Res> {
  factory $EnvironmentConfigCopyWith(
          EnvironmentConfig value, $Res Function(EnvironmentConfig) then) =
      _$EnvironmentConfigCopyWithImpl<$Res, EnvironmentConfig>;
  @useResult
  $Res call(
      {bool isDebug,
      bool enableLogging,
      bool enablePerformanceMonitoring,
      bool enableCrashReporting,
      String logLevel});
}

/// @nodoc
class _$EnvironmentConfigCopyWithImpl<$Res, $Val extends EnvironmentConfig>
    implements $EnvironmentConfigCopyWith<$Res> {
  _$EnvironmentConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDebug = null,
    Object? enableLogging = null,
    Object? enablePerformanceMonitoring = null,
    Object? enableCrashReporting = null,
    Object? logLevel = null,
  }) {
    return _then(_value.copyWith(
      isDebug: null == isDebug
          ? _value.isDebug
          : isDebug // ignore: cast_nullable_to_non_nullable
              as bool,
      enableLogging: null == enableLogging
          ? _value.enableLogging
          : enableLogging // ignore: cast_nullable_to_non_nullable
              as bool,
      enablePerformanceMonitoring: null == enablePerformanceMonitoring
          ? _value.enablePerformanceMonitoring
          : enablePerformanceMonitoring // ignore: cast_nullable_to_non_nullable
              as bool,
      enableCrashReporting: null == enableCrashReporting
          ? _value.enableCrashReporting
          : enableCrashReporting // ignore: cast_nullable_to_non_nullable
              as bool,
      logLevel: null == logLevel
          ? _value.logLevel
          : logLevel // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EnvironmentConfigImplCopyWith<$Res>
    implements $EnvironmentConfigCopyWith<$Res> {
  factory _$$EnvironmentConfigImplCopyWith(_$EnvironmentConfigImpl value,
          $Res Function(_$EnvironmentConfigImpl) then) =
      __$$EnvironmentConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isDebug,
      bool enableLogging,
      bool enablePerformanceMonitoring,
      bool enableCrashReporting,
      String logLevel});
}

/// @nodoc
class __$$EnvironmentConfigImplCopyWithImpl<$Res>
    extends _$EnvironmentConfigCopyWithImpl<$Res, _$EnvironmentConfigImpl>
    implements _$$EnvironmentConfigImplCopyWith<$Res> {
  __$$EnvironmentConfigImplCopyWithImpl(_$EnvironmentConfigImpl _value,
      $Res Function(_$EnvironmentConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of EnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDebug = null,
    Object? enableLogging = null,
    Object? enablePerformanceMonitoring = null,
    Object? enableCrashReporting = null,
    Object? logLevel = null,
  }) {
    return _then(_$EnvironmentConfigImpl(
      isDebug: null == isDebug
          ? _value.isDebug
          : isDebug // ignore: cast_nullable_to_non_nullable
              as bool,
      enableLogging: null == enableLogging
          ? _value.enableLogging
          : enableLogging // ignore: cast_nullable_to_non_nullable
              as bool,
      enablePerformanceMonitoring: null == enablePerformanceMonitoring
          ? _value.enablePerformanceMonitoring
          : enablePerformanceMonitoring // ignore: cast_nullable_to_non_nullable
              as bool,
      enableCrashReporting: null == enableCrashReporting
          ? _value.enableCrashReporting
          : enableCrashReporting // ignore: cast_nullable_to_non_nullable
              as bool,
      logLevel: null == logLevel
          ? _value.logLevel
          : logLevel // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EnvironmentConfigImpl implements _EnvironmentConfig {
  const _$EnvironmentConfigImpl(
      {required this.isDebug,
      required this.enableLogging,
      required this.enablePerformanceMonitoring,
      required this.enableCrashReporting,
      required this.logLevel});

  @override
  final bool isDebug;
  @override
  final bool enableLogging;
  @override
  final bool enablePerformanceMonitoring;
  @override
  final bool enableCrashReporting;
  @override
  final String logLevel;

  @override
  String toString() {
    return 'EnvironmentConfig(isDebug: $isDebug, enableLogging: $enableLogging, enablePerformanceMonitoring: $enablePerformanceMonitoring, enableCrashReporting: $enableCrashReporting, logLevel: $logLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EnvironmentConfigImpl &&
            (identical(other.isDebug, isDebug) || other.isDebug == isDebug) &&
            (identical(other.enableLogging, enableLogging) ||
                other.enableLogging == enableLogging) &&
            (identical(other.enablePerformanceMonitoring,
                    enablePerformanceMonitoring) ||
                other.enablePerformanceMonitoring ==
                    enablePerformanceMonitoring) &&
            (identical(other.enableCrashReporting, enableCrashReporting) ||
                other.enableCrashReporting == enableCrashReporting) &&
            (identical(other.logLevel, logLevel) ||
                other.logLevel == logLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isDebug, enableLogging,
      enablePerformanceMonitoring, enableCrashReporting, logLevel);

  /// Create a copy of EnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EnvironmentConfigImplCopyWith<_$EnvironmentConfigImpl> get copyWith =>
      __$$EnvironmentConfigImplCopyWithImpl<_$EnvironmentConfigImpl>(
          this, _$identity);
}

abstract class _EnvironmentConfig implements EnvironmentConfig {
  const factory _EnvironmentConfig(
      {required final bool isDebug,
      required final bool enableLogging,
      required final bool enablePerformanceMonitoring,
      required final bool enableCrashReporting,
      required final String logLevel}) = _$EnvironmentConfigImpl;

  @override
  bool get isDebug;
  @override
  bool get enableLogging;
  @override
  bool get enablePerformanceMonitoring;
  @override
  bool get enableCrashReporting;
  @override
  String get logLevel;

  /// Create a copy of EnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EnvironmentConfigImplCopyWith<_$EnvironmentConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
