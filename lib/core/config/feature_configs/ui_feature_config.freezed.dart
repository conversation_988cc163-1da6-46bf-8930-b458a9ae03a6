// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ui_feature_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UiFeatureConfig {
  bool get showDebugVersionTag => throw _privateConstructorUsedError;
  bool get showTradingWallet => throw _privateConstructorUsedError;
  bool get showTransferPreview => throw _privateConstructorUsedError;
  bool get showTransfer => throw _privateConstructorUsedError;
  bool get showMentorVipLevel => throw _privateConstructorUsedError;
  bool get showWithdrawHistory => throw _privateConstructorUsedError;
  bool get showAppUpdate => throw _privateConstructorUsedError;
  MarketColor get marketColor => throw _privateConstructorUsedError;
  bool get disableUpperCasePasswordProtection =>
      throw _privateConstructorUsedError;
  bool get fetchCommunityRecords => throw _privateConstructorUsedError;

  /// Create a copy of UiFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UiFeatureConfigCopyWith<UiFeatureConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UiFeatureConfigCopyWith<$Res> {
  factory $UiFeatureConfigCopyWith(
          UiFeatureConfig value, $Res Function(UiFeatureConfig) then) =
      _$UiFeatureConfigCopyWithImpl<$Res, UiFeatureConfig>;
  @useResult
  $Res call(
      {bool showDebugVersionTag,
      bool showTradingWallet,
      bool showTransferPreview,
      bool showTransfer,
      bool showMentorVipLevel,
      bool showWithdrawHistory,
      bool showAppUpdate,
      MarketColor marketColor,
      bool disableUpperCasePasswordProtection,
      bool fetchCommunityRecords});
}

/// @nodoc
class _$UiFeatureConfigCopyWithImpl<$Res, $Val extends UiFeatureConfig>
    implements $UiFeatureConfigCopyWith<$Res> {
  _$UiFeatureConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UiFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDebugVersionTag = null,
    Object? showTradingWallet = null,
    Object? showTransferPreview = null,
    Object? showTransfer = null,
    Object? showMentorVipLevel = null,
    Object? showWithdrawHistory = null,
    Object? showAppUpdate = null,
    Object? marketColor = null,
    Object? disableUpperCasePasswordProtection = null,
    Object? fetchCommunityRecords = null,
  }) {
    return _then(_value.copyWith(
      showDebugVersionTag: null == showDebugVersionTag
          ? _value.showDebugVersionTag
          : showDebugVersionTag // ignore: cast_nullable_to_non_nullable
              as bool,
      showTradingWallet: null == showTradingWallet
          ? _value.showTradingWallet
          : showTradingWallet // ignore: cast_nullable_to_non_nullable
              as bool,
      showTransferPreview: null == showTransferPreview
          ? _value.showTransferPreview
          : showTransferPreview // ignore: cast_nullable_to_non_nullable
              as bool,
      showTransfer: null == showTransfer
          ? _value.showTransfer
          : showTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      showMentorVipLevel: null == showMentorVipLevel
          ? _value.showMentorVipLevel
          : showMentorVipLevel // ignore: cast_nullable_to_non_nullable
              as bool,
      showWithdrawHistory: null == showWithdrawHistory
          ? _value.showWithdrawHistory
          : showWithdrawHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      showAppUpdate: null == showAppUpdate
          ? _value.showAppUpdate
          : showAppUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
      marketColor: null == marketColor
          ? _value.marketColor
          : marketColor // ignore: cast_nullable_to_non_nullable
              as MarketColor,
      disableUpperCasePasswordProtection: null ==
              disableUpperCasePasswordProtection
          ? _value.disableUpperCasePasswordProtection
          : disableUpperCasePasswordProtection // ignore: cast_nullable_to_non_nullable
              as bool,
      fetchCommunityRecords: null == fetchCommunityRecords
          ? _value.fetchCommunityRecords
          : fetchCommunityRecords // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UiFeatureConfigImplCopyWith<$Res>
    implements $UiFeatureConfigCopyWith<$Res> {
  factory _$$UiFeatureConfigImplCopyWith(_$UiFeatureConfigImpl value,
          $Res Function(_$UiFeatureConfigImpl) then) =
      __$$UiFeatureConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showDebugVersionTag,
      bool showTradingWallet,
      bool showTransferPreview,
      bool showTransfer,
      bool showMentorVipLevel,
      bool showWithdrawHistory,
      bool showAppUpdate,
      MarketColor marketColor,
      bool disableUpperCasePasswordProtection,
      bool fetchCommunityRecords});
}

/// @nodoc
class __$$UiFeatureConfigImplCopyWithImpl<$Res>
    extends _$UiFeatureConfigCopyWithImpl<$Res, _$UiFeatureConfigImpl>
    implements _$$UiFeatureConfigImplCopyWith<$Res> {
  __$$UiFeatureConfigImplCopyWithImpl(
      _$UiFeatureConfigImpl _value, $Res Function(_$UiFeatureConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of UiFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDebugVersionTag = null,
    Object? showTradingWallet = null,
    Object? showTransferPreview = null,
    Object? showTransfer = null,
    Object? showMentorVipLevel = null,
    Object? showWithdrawHistory = null,
    Object? showAppUpdate = null,
    Object? marketColor = null,
    Object? disableUpperCasePasswordProtection = null,
    Object? fetchCommunityRecords = null,
  }) {
    return _then(_$UiFeatureConfigImpl(
      showDebugVersionTag: null == showDebugVersionTag
          ? _value.showDebugVersionTag
          : showDebugVersionTag // ignore: cast_nullable_to_non_nullable
              as bool,
      showTradingWallet: null == showTradingWallet
          ? _value.showTradingWallet
          : showTradingWallet // ignore: cast_nullable_to_non_nullable
              as bool,
      showTransferPreview: null == showTransferPreview
          ? _value.showTransferPreview
          : showTransferPreview // ignore: cast_nullable_to_non_nullable
              as bool,
      showTransfer: null == showTransfer
          ? _value.showTransfer
          : showTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      showMentorVipLevel: null == showMentorVipLevel
          ? _value.showMentorVipLevel
          : showMentorVipLevel // ignore: cast_nullable_to_non_nullable
              as bool,
      showWithdrawHistory: null == showWithdrawHistory
          ? _value.showWithdrawHistory
          : showWithdrawHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      showAppUpdate: null == showAppUpdate
          ? _value.showAppUpdate
          : showAppUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
      marketColor: null == marketColor
          ? _value.marketColor
          : marketColor // ignore: cast_nullable_to_non_nullable
              as MarketColor,
      disableUpperCasePasswordProtection: null ==
              disableUpperCasePasswordProtection
          ? _value.disableUpperCasePasswordProtection
          : disableUpperCasePasswordProtection // ignore: cast_nullable_to_non_nullable
              as bool,
      fetchCommunityRecords: null == fetchCommunityRecords
          ? _value.fetchCommunityRecords
          : fetchCommunityRecords // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$UiFeatureConfigImpl implements _UiFeatureConfig {
  const _$UiFeatureConfigImpl(
      {required this.showDebugVersionTag,
      required this.showTradingWallet,
      required this.showTransferPreview,
      required this.showTransfer,
      required this.showMentorVipLevel,
      required this.showWithdrawHistory,
      required this.showAppUpdate,
      required this.marketColor,
      required this.disableUpperCasePasswordProtection,
      required this.fetchCommunityRecords});

  @override
  final bool showDebugVersionTag;
  @override
  final bool showTradingWallet;
  @override
  final bool showTransferPreview;
  @override
  final bool showTransfer;
  @override
  final bool showMentorVipLevel;
  @override
  final bool showWithdrawHistory;
  @override
  final bool showAppUpdate;
  @override
  final MarketColor marketColor;
  @override
  final bool disableUpperCasePasswordProtection;
  @override
  final bool fetchCommunityRecords;

  @override
  String toString() {
    return 'UiFeatureConfig(showDebugVersionTag: $showDebugVersionTag, showTradingWallet: $showTradingWallet, showTransferPreview: $showTransferPreview, showTransfer: $showTransfer, showMentorVipLevel: $showMentorVipLevel, showWithdrawHistory: $showWithdrawHistory, showAppUpdate: $showAppUpdate, marketColor: $marketColor, disableUpperCasePasswordProtection: $disableUpperCasePasswordProtection, fetchCommunityRecords: $fetchCommunityRecords)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UiFeatureConfigImpl &&
            (identical(other.showDebugVersionTag, showDebugVersionTag) ||
                other.showDebugVersionTag == showDebugVersionTag) &&
            (identical(other.showTradingWallet, showTradingWallet) ||
                other.showTradingWallet == showTradingWallet) &&
            (identical(other.showTransferPreview, showTransferPreview) ||
                other.showTransferPreview == showTransferPreview) &&
            (identical(other.showTransfer, showTransfer) ||
                other.showTransfer == showTransfer) &&
            (identical(other.showMentorVipLevel, showMentorVipLevel) ||
                other.showMentorVipLevel == showMentorVipLevel) &&
            (identical(other.showWithdrawHistory, showWithdrawHistory) ||
                other.showWithdrawHistory == showWithdrawHistory) &&
            (identical(other.showAppUpdate, showAppUpdate) ||
                other.showAppUpdate == showAppUpdate) &&
            (identical(other.marketColor, marketColor) ||
                other.marketColor == marketColor) &&
            (identical(other.disableUpperCasePasswordProtection,
                    disableUpperCasePasswordProtection) ||
                other.disableUpperCasePasswordProtection ==
                    disableUpperCasePasswordProtection) &&
            (identical(other.fetchCommunityRecords, fetchCommunityRecords) ||
                other.fetchCommunityRecords == fetchCommunityRecords));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      showDebugVersionTag,
      showTradingWallet,
      showTransferPreview,
      showTransfer,
      showMentorVipLevel,
      showWithdrawHistory,
      showAppUpdate,
      marketColor,
      disableUpperCasePasswordProtection,
      fetchCommunityRecords);

  /// Create a copy of UiFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UiFeatureConfigImplCopyWith<_$UiFeatureConfigImpl> get copyWith =>
      __$$UiFeatureConfigImplCopyWithImpl<_$UiFeatureConfigImpl>(
          this, _$identity);
}

abstract class _UiFeatureConfig implements UiFeatureConfig {
  const factory _UiFeatureConfig(
      {required final bool showDebugVersionTag,
      required final bool showTradingWallet,
      required final bool showTransferPreview,
      required final bool showTransfer,
      required final bool showMentorVipLevel,
      required final bool showWithdrawHistory,
      required final bool showAppUpdate,
      required final MarketColor marketColor,
      required final bool disableUpperCasePasswordProtection,
      required final bool fetchCommunityRecords}) = _$UiFeatureConfigImpl;

  @override
  bool get showDebugVersionTag;
  @override
  bool get showTradingWallet;
  @override
  bool get showTransferPreview;
  @override
  bool get showTransfer;
  @override
  bool get showMentorVipLevel;
  @override
  bool get showWithdrawHistory;
  @override
  bool get showAppUpdate;
  @override
  MarketColor get marketColor;
  @override
  bool get disableUpperCasePasswordProtection;
  @override
  bool get fetchCommunityRecords;

  /// Create a copy of UiFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UiFeatureConfigImplCopyWith<_$UiFeatureConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
