// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_feature_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentFeatureConfig {
  bool get useStaticPaymentTypes => throw _privateConstructorUsedError;
  bool get shouldFetchWalletCoinsFromApi => throw _privateConstructorUsedError;
  bool get showAddWalletAddress => throw _privateConstructorUsedError;
  bool get showDepositeTxHash => throw _privateConstructorUsedError;
  List<PaymentTypeModel> get staticPaymentTypes =>
      throw _privateConstructorUsedError;
  List<PaymentTypeModel> get staticWithdrawAddressTypes =>
      throw _privateConstructorUsedError;

  /// Create a copy of PaymentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentFeatureConfigCopyWith<PaymentFeatureConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentFeatureConfigCopyWith<$Res> {
  factory $PaymentFeatureConfigCopyWith(PaymentFeatureConfig value,
          $Res Function(PaymentFeatureConfig) then) =
      _$PaymentFeatureConfigCopyWithImpl<$Res, PaymentFeatureConfig>;
  @useResult
  $Res call(
      {bool useStaticPaymentTypes,
      bool shouldFetchWalletCoinsFromApi,
      bool showAddWalletAddress,
      bool showDepositeTxHash,
      List<PaymentTypeModel> staticPaymentTypes,
      List<PaymentTypeModel> staticWithdrawAddressTypes});
}

/// @nodoc
class _$PaymentFeatureConfigCopyWithImpl<$Res,
        $Val extends PaymentFeatureConfig>
    implements $PaymentFeatureConfigCopyWith<$Res> {
  _$PaymentFeatureConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? useStaticPaymentTypes = null,
    Object? shouldFetchWalletCoinsFromApi = null,
    Object? showAddWalletAddress = null,
    Object? showDepositeTxHash = null,
    Object? staticPaymentTypes = null,
    Object? staticWithdrawAddressTypes = null,
  }) {
    return _then(_value.copyWith(
      useStaticPaymentTypes: null == useStaticPaymentTypes
          ? _value.useStaticPaymentTypes
          : useStaticPaymentTypes // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldFetchWalletCoinsFromApi: null == shouldFetchWalletCoinsFromApi
          ? _value.shouldFetchWalletCoinsFromApi
          : shouldFetchWalletCoinsFromApi // ignore: cast_nullable_to_non_nullable
              as bool,
      showAddWalletAddress: null == showAddWalletAddress
          ? _value.showAddWalletAddress
          : showAddWalletAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      showDepositeTxHash: null == showDepositeTxHash
          ? _value.showDepositeTxHash
          : showDepositeTxHash // ignore: cast_nullable_to_non_nullable
              as bool,
      staticPaymentTypes: null == staticPaymentTypes
          ? _value.staticPaymentTypes
          : staticPaymentTypes // ignore: cast_nullable_to_non_nullable
              as List<PaymentTypeModel>,
      staticWithdrawAddressTypes: null == staticWithdrawAddressTypes
          ? _value.staticWithdrawAddressTypes
          : staticWithdrawAddressTypes // ignore: cast_nullable_to_non_nullable
              as List<PaymentTypeModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentFeatureConfigImplCopyWith<$Res>
    implements $PaymentFeatureConfigCopyWith<$Res> {
  factory _$$PaymentFeatureConfigImplCopyWith(_$PaymentFeatureConfigImpl value,
          $Res Function(_$PaymentFeatureConfigImpl) then) =
      __$$PaymentFeatureConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool useStaticPaymentTypes,
      bool shouldFetchWalletCoinsFromApi,
      bool showAddWalletAddress,
      bool showDepositeTxHash,
      List<PaymentTypeModel> staticPaymentTypes,
      List<PaymentTypeModel> staticWithdrawAddressTypes});
}

/// @nodoc
class __$$PaymentFeatureConfigImplCopyWithImpl<$Res>
    extends _$PaymentFeatureConfigCopyWithImpl<$Res, _$PaymentFeatureConfigImpl>
    implements _$$PaymentFeatureConfigImplCopyWith<$Res> {
  __$$PaymentFeatureConfigImplCopyWithImpl(_$PaymentFeatureConfigImpl _value,
      $Res Function(_$PaymentFeatureConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? useStaticPaymentTypes = null,
    Object? shouldFetchWalletCoinsFromApi = null,
    Object? showAddWalletAddress = null,
    Object? showDepositeTxHash = null,
    Object? staticPaymentTypes = null,
    Object? staticWithdrawAddressTypes = null,
  }) {
    return _then(_$PaymentFeatureConfigImpl(
      useStaticPaymentTypes: null == useStaticPaymentTypes
          ? _value.useStaticPaymentTypes
          : useStaticPaymentTypes // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldFetchWalletCoinsFromApi: null == shouldFetchWalletCoinsFromApi
          ? _value.shouldFetchWalletCoinsFromApi
          : shouldFetchWalletCoinsFromApi // ignore: cast_nullable_to_non_nullable
              as bool,
      showAddWalletAddress: null == showAddWalletAddress
          ? _value.showAddWalletAddress
          : showAddWalletAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      showDepositeTxHash: null == showDepositeTxHash
          ? _value.showDepositeTxHash
          : showDepositeTxHash // ignore: cast_nullable_to_non_nullable
              as bool,
      staticPaymentTypes: null == staticPaymentTypes
          ? _value._staticPaymentTypes
          : staticPaymentTypes // ignore: cast_nullable_to_non_nullable
              as List<PaymentTypeModel>,
      staticWithdrawAddressTypes: null == staticWithdrawAddressTypes
          ? _value._staticWithdrawAddressTypes
          : staticWithdrawAddressTypes // ignore: cast_nullable_to_non_nullable
              as List<PaymentTypeModel>,
    ));
  }
}

/// @nodoc

class _$PaymentFeatureConfigImpl implements _PaymentFeatureConfig {
  const _$PaymentFeatureConfigImpl(
      {required this.useStaticPaymentTypes,
      required this.shouldFetchWalletCoinsFromApi,
      required this.showAddWalletAddress,
      required this.showDepositeTxHash,
      required final List<PaymentTypeModel> staticPaymentTypes,
      required final List<PaymentTypeModel> staticWithdrawAddressTypes})
      : _staticPaymentTypes = staticPaymentTypes,
        _staticWithdrawAddressTypes = staticWithdrawAddressTypes;

  @override
  final bool useStaticPaymentTypes;
  @override
  final bool shouldFetchWalletCoinsFromApi;
  @override
  final bool showAddWalletAddress;
  @override
  final bool showDepositeTxHash;
  final List<PaymentTypeModel> _staticPaymentTypes;
  @override
  List<PaymentTypeModel> get staticPaymentTypes {
    if (_staticPaymentTypes is EqualUnmodifiableListView)
      return _staticPaymentTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_staticPaymentTypes);
  }

  final List<PaymentTypeModel> _staticWithdrawAddressTypes;
  @override
  List<PaymentTypeModel> get staticWithdrawAddressTypes {
    if (_staticWithdrawAddressTypes is EqualUnmodifiableListView)
      return _staticWithdrawAddressTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_staticWithdrawAddressTypes);
  }

  @override
  String toString() {
    return 'PaymentFeatureConfig(useStaticPaymentTypes: $useStaticPaymentTypes, shouldFetchWalletCoinsFromApi: $shouldFetchWalletCoinsFromApi, showAddWalletAddress: $showAddWalletAddress, showDepositeTxHash: $showDepositeTxHash, staticPaymentTypes: $staticPaymentTypes, staticWithdrawAddressTypes: $staticWithdrawAddressTypes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentFeatureConfigImpl &&
            (identical(other.useStaticPaymentTypes, useStaticPaymentTypes) ||
                other.useStaticPaymentTypes == useStaticPaymentTypes) &&
            (identical(other.shouldFetchWalletCoinsFromApi,
                    shouldFetchWalletCoinsFromApi) ||
                other.shouldFetchWalletCoinsFromApi ==
                    shouldFetchWalletCoinsFromApi) &&
            (identical(other.showAddWalletAddress, showAddWalletAddress) ||
                other.showAddWalletAddress == showAddWalletAddress) &&
            (identical(other.showDepositeTxHash, showDepositeTxHash) ||
                other.showDepositeTxHash == showDepositeTxHash) &&
            const DeepCollectionEquality()
                .equals(other._staticPaymentTypes, _staticPaymentTypes) &&
            const DeepCollectionEquality().equals(
                other._staticWithdrawAddressTypes,
                _staticWithdrawAddressTypes));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      useStaticPaymentTypes,
      shouldFetchWalletCoinsFromApi,
      showAddWalletAddress,
      showDepositeTxHash,
      const DeepCollectionEquality().hash(_staticPaymentTypes),
      const DeepCollectionEquality().hash(_staticWithdrawAddressTypes));

  /// Create a copy of PaymentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentFeatureConfigImplCopyWith<_$PaymentFeatureConfigImpl>
      get copyWith =>
          __$$PaymentFeatureConfigImplCopyWithImpl<_$PaymentFeatureConfigImpl>(
              this, _$identity);
}

abstract class _PaymentFeatureConfig implements PaymentFeatureConfig {
  const factory _PaymentFeatureConfig(
          {required final bool useStaticPaymentTypes,
          required final bool shouldFetchWalletCoinsFromApi,
          required final bool showAddWalletAddress,
          required final bool showDepositeTxHash,
          required final List<PaymentTypeModel> staticPaymentTypes,
          required final List<PaymentTypeModel> staticWithdrawAddressTypes}) =
      _$PaymentFeatureConfigImpl;

  @override
  bool get useStaticPaymentTypes;
  @override
  bool get shouldFetchWalletCoinsFromApi;
  @override
  bool get showAddWalletAddress;
  @override
  bool get showDepositeTxHash;
  @override
  List<PaymentTypeModel> get staticPaymentTypes;
  @override
  List<PaymentTypeModel> get staticWithdrawAddressTypes;

  /// Create a copy of PaymentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentFeatureConfigImplCopyWith<_$PaymentFeatureConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
