import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../features/smart_investment/domain/models/order_rate/order_rate.dart';
import '../../../features/smart_investment/domain/models/purchase_percentage/purchase_percentage.dart';

part 'investment_feature_config.freezed.dart';

/// Configuration for investment-related features
/// 
/// This configuration handles smart investment features, purchase percentages,
/// and investment-related feature flags.
@freezed
class InvestmentFeatureConfig with _$InvestmentFeatureConfig {
  const factory InvestmentFeatureConfig({
    required bool showSmartInvestmentPurchasePercentage,
    required bool useStaticPurchasePercentage,
    required bool showPurchaseProductFields,
    required bool showBenefitRules,
    required List<PurchasePercentage> staticPurchasePercentages,
  }) = _InvestmentFeatureConfig;

  /// Creates a static investment configuration
  factory InvestmentFeatureConfig.static({
    bool showSmartInvestmentPurchasePercentage = true,
    bool showPurchaseProductFields = false,
    bool showBenefitRules = false,
  }) =>
      InvestmentFeatureConfig(
        showSmartInvestmentPurchasePercentage: showSmartInvestmentPurchasePercentage,
        useStaticPurchasePercentage: true,
        showPurchaseProductFields: showPurchaseProductFields,
        showBenefitRules: showBenefitRules,
        staticPurchasePercentages: _defaultStaticPercentages,
      );

  /// Creates an API-based investment configuration
  factory InvestmentFeatureConfig.api({
    bool showSmartInvestmentPurchasePercentage = true,
    bool showPurchaseProductFields = true,
    bool showBenefitRules = true,
  }) =>
      InvestmentFeatureConfig(
        showSmartInvestmentPurchasePercentage: showSmartInvestmentPurchasePercentage,
        useStaticPurchasePercentage: false,
        showPurchaseProductFields: showPurchaseProductFields,
        showBenefitRules: showBenefitRules,
        staticPurchasePercentages: _defaultStaticPercentages, // fallback
      );

  /// Default static purchase percentages
  static final List<PurchasePercentage> _defaultStaticPercentages = [
    PurchasePercentage(percentage: '30', isSelected: false, type: 1),
    PurchasePercentage(percentage: '50', isSelected: false, type: 2),
    PurchasePercentage(percentage: '100', isSelected: true, type: 3),
  ];
}

/// Extension methods for investment configuration logic
extension InvestmentFeatureConfigExtension on InvestmentFeatureConfig {
  /// Determines if API mode should be used for purchase percentages
  bool isApiMode(OrderRateData? apiData) {
    return showSmartInvestmentPurchasePercentage &&
        !useStaticPurchasePercentage &&
        !isApiConfigEmpty(apiData);
  }

  /// Checks if API configuration is empty or invalid
  bool isApiConfigEmpty(OrderRateData? apiData) {
    return apiData == null ||
        apiData.orderRates == null ||
        apiData.orderRates!.isEmpty;
  }

  /// Determines if purchase percentage options should be displayed
  bool shouldShowPurchasePercentage({OrderRateData? apiConfigData}) {
    return showSmartInvestmentPurchasePercentage &&
        (useStaticPurchasePercentage || !isApiConfigEmpty(apiConfigData));
  }

  /// Determines if purchase percentages should be fetched from API
  bool get shouldFetchPurchasePercentageFromApi {
    return showSmartInvestmentPurchasePercentage && !useStaticPurchasePercentage;
  }

  /// Gets available purchase percentages
  List<PurchasePercentage> getPurchasePercentageList({
    OrderRateData? apiData,
    String? selected,
  }) {
    if (isApiMode(apiData)) {
      return _getApiPercentages(apiData!, selected);
    }
    return _getStaticPercentages(selected);
  }

  /// Gets default purchase percentage
  String getDefaultPurchasePercentage({OrderRateData? apiData}) {
    if (isApiMode(apiData)) {
      final maxRate = apiData!.orderRates!.reduce((a, b) => a > b ? a : b);
      return maxRate.toString();
    }
    return '100';
  }

  /// Checks if a percentage is enabled
  bool isPurchasePercentageEnabled(String percentage, {OrderRateData? apiData}) {
    if (isApiMode(apiData)) {
      final value = int.tryParse(percentage);
      return value != null && apiData!.orderRates!.contains(value);
    }
    return percentage == '100'; // Only 100% enabled in static mode by default
  }

  /// Private helper for API percentages
  List<PurchasePercentage> _getApiPercentages(OrderRateData apiData, String? selected) {
    final defaultPercentage = getDefaultPurchasePercentage(apiData: apiData);
    final selectedPercentage = selected ?? defaultPercentage;
    
    return apiData.orderRates!
        .map((rate) => PurchasePercentage(
              percentage: rate.toString(),
              isSelected: rate.toString() == selectedPercentage,
              type: rate,
            ))
        .toList();
  }

  /// Private helper for static percentages
  List<PurchasePercentage> _getStaticPercentages(String? selected) {
    final selectedPercentage = selected ?? '100';
    return staticPurchasePercentages
        .map((item) => PurchasePercentage(
              percentage: item.percentage,
              isSelected: item.percentage == selectedPercentage,
              type: item.type,
            ))
        .toList();
  }
}
