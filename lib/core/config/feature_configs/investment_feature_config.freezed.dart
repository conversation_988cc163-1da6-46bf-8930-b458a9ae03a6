// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'investment_feature_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InvestmentFeatureConfig {
  bool get showSmartInvestmentPurchasePercentage =>
      throw _privateConstructorUsedError;
  bool get useStaticPurchasePercentage => throw _privateConstructorUsedError;
  bool get showPurchaseProductFields => throw _privateConstructorUsedError;
  bool get showBenefitRules => throw _privateConstructorUsedError;
  List<PurchasePercentage> get staticPurchasePercentages =>
      throw _privateConstructorUsedError;

  /// Create a copy of InvestmentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvestmentFeatureConfigCopyWith<InvestmentFeatureConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvestmentFeatureConfigCopyWith<$Res> {
  factory $InvestmentFeatureConfigCopyWith(InvestmentFeatureConfig value,
          $Res Function(InvestmentFeatureConfig) then) =
      _$InvestmentFeatureConfigCopyWithImpl<$Res, InvestmentFeatureConfig>;
  @useResult
  $Res call(
      {bool showSmartInvestmentPurchasePercentage,
      bool useStaticPurchasePercentage,
      bool showPurchaseProductFields,
      bool showBenefitRules,
      List<PurchasePercentage> staticPurchasePercentages});
}

/// @nodoc
class _$InvestmentFeatureConfigCopyWithImpl<$Res,
        $Val extends InvestmentFeatureConfig>
    implements $InvestmentFeatureConfigCopyWith<$Res> {
  _$InvestmentFeatureConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvestmentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showSmartInvestmentPurchasePercentage = null,
    Object? useStaticPurchasePercentage = null,
    Object? showPurchaseProductFields = null,
    Object? showBenefitRules = null,
    Object? staticPurchasePercentages = null,
  }) {
    return _then(_value.copyWith(
      showSmartInvestmentPurchasePercentage: null ==
              showSmartInvestmentPurchasePercentage
          ? _value.showSmartInvestmentPurchasePercentage
          : showSmartInvestmentPurchasePercentage // ignore: cast_nullable_to_non_nullable
              as bool,
      useStaticPurchasePercentage: null == useStaticPurchasePercentage
          ? _value.useStaticPurchasePercentage
          : useStaticPurchasePercentage // ignore: cast_nullable_to_non_nullable
              as bool,
      showPurchaseProductFields: null == showPurchaseProductFields
          ? _value.showPurchaseProductFields
          : showPurchaseProductFields // ignore: cast_nullable_to_non_nullable
              as bool,
      showBenefitRules: null == showBenefitRules
          ? _value.showBenefitRules
          : showBenefitRules // ignore: cast_nullable_to_non_nullable
              as bool,
      staticPurchasePercentages: null == staticPurchasePercentages
          ? _value.staticPurchasePercentages
          : staticPurchasePercentages // ignore: cast_nullable_to_non_nullable
              as List<PurchasePercentage>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InvestmentFeatureConfigImplCopyWith<$Res>
    implements $InvestmentFeatureConfigCopyWith<$Res> {
  factory _$$InvestmentFeatureConfigImplCopyWith(
          _$InvestmentFeatureConfigImpl value,
          $Res Function(_$InvestmentFeatureConfigImpl) then) =
      __$$InvestmentFeatureConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showSmartInvestmentPurchasePercentage,
      bool useStaticPurchasePercentage,
      bool showPurchaseProductFields,
      bool showBenefitRules,
      List<PurchasePercentage> staticPurchasePercentages});
}

/// @nodoc
class __$$InvestmentFeatureConfigImplCopyWithImpl<$Res>
    extends _$InvestmentFeatureConfigCopyWithImpl<$Res,
        _$InvestmentFeatureConfigImpl>
    implements _$$InvestmentFeatureConfigImplCopyWith<$Res> {
  __$$InvestmentFeatureConfigImplCopyWithImpl(
      _$InvestmentFeatureConfigImpl _value,
      $Res Function(_$InvestmentFeatureConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvestmentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showSmartInvestmentPurchasePercentage = null,
    Object? useStaticPurchasePercentage = null,
    Object? showPurchaseProductFields = null,
    Object? showBenefitRules = null,
    Object? staticPurchasePercentages = null,
  }) {
    return _then(_$InvestmentFeatureConfigImpl(
      showSmartInvestmentPurchasePercentage: null ==
              showSmartInvestmentPurchasePercentage
          ? _value.showSmartInvestmentPurchasePercentage
          : showSmartInvestmentPurchasePercentage // ignore: cast_nullable_to_non_nullable
              as bool,
      useStaticPurchasePercentage: null == useStaticPurchasePercentage
          ? _value.useStaticPurchasePercentage
          : useStaticPurchasePercentage // ignore: cast_nullable_to_non_nullable
              as bool,
      showPurchaseProductFields: null == showPurchaseProductFields
          ? _value.showPurchaseProductFields
          : showPurchaseProductFields // ignore: cast_nullable_to_non_nullable
              as bool,
      showBenefitRules: null == showBenefitRules
          ? _value.showBenefitRules
          : showBenefitRules // ignore: cast_nullable_to_non_nullable
              as bool,
      staticPurchasePercentages: null == staticPurchasePercentages
          ? _value._staticPurchasePercentages
          : staticPurchasePercentages // ignore: cast_nullable_to_non_nullable
              as List<PurchasePercentage>,
    ));
  }
}

/// @nodoc

class _$InvestmentFeatureConfigImpl implements _InvestmentFeatureConfig {
  const _$InvestmentFeatureConfigImpl(
      {required this.showSmartInvestmentPurchasePercentage,
      required this.useStaticPurchasePercentage,
      required this.showPurchaseProductFields,
      required this.showBenefitRules,
      required final List<PurchasePercentage> staticPurchasePercentages})
      : _staticPurchasePercentages = staticPurchasePercentages;

  @override
  final bool showSmartInvestmentPurchasePercentage;
  @override
  final bool useStaticPurchasePercentage;
  @override
  final bool showPurchaseProductFields;
  @override
  final bool showBenefitRules;
  final List<PurchasePercentage> _staticPurchasePercentages;
  @override
  List<PurchasePercentage> get staticPurchasePercentages {
    if (_staticPurchasePercentages is EqualUnmodifiableListView)
      return _staticPurchasePercentages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_staticPurchasePercentages);
  }

  @override
  String toString() {
    return 'InvestmentFeatureConfig(showSmartInvestmentPurchasePercentage: $showSmartInvestmentPurchasePercentage, useStaticPurchasePercentage: $useStaticPurchasePercentage, showPurchaseProductFields: $showPurchaseProductFields, showBenefitRules: $showBenefitRules, staticPurchasePercentages: $staticPurchasePercentages)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvestmentFeatureConfigImpl &&
            (identical(other.showSmartInvestmentPurchasePercentage,
                    showSmartInvestmentPurchasePercentage) ||
                other.showSmartInvestmentPurchasePercentage ==
                    showSmartInvestmentPurchasePercentage) &&
            (identical(other.useStaticPurchasePercentage,
                    useStaticPurchasePercentage) ||
                other.useStaticPurchasePercentage ==
                    useStaticPurchasePercentage) &&
            (identical(other.showPurchaseProductFields,
                    showPurchaseProductFields) ||
                other.showPurchaseProductFields == showPurchaseProductFields) &&
            (identical(other.showBenefitRules, showBenefitRules) ||
                other.showBenefitRules == showBenefitRules) &&
            const DeepCollectionEquality().equals(
                other._staticPurchasePercentages, _staticPurchasePercentages));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      showSmartInvestmentPurchasePercentage,
      useStaticPurchasePercentage,
      showPurchaseProductFields,
      showBenefitRules,
      const DeepCollectionEquality().hash(_staticPurchasePercentages));

  /// Create a copy of InvestmentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvestmentFeatureConfigImplCopyWith<_$InvestmentFeatureConfigImpl>
      get copyWith => __$$InvestmentFeatureConfigImplCopyWithImpl<
          _$InvestmentFeatureConfigImpl>(this, _$identity);
}

abstract class _InvestmentFeatureConfig implements InvestmentFeatureConfig {
  const factory _InvestmentFeatureConfig(
          {required final bool showSmartInvestmentPurchasePercentage,
          required final bool useStaticPurchasePercentage,
          required final bool showPurchaseProductFields,
          required final bool showBenefitRules,
          required final List<PurchasePercentage> staticPurchasePercentages}) =
      _$InvestmentFeatureConfigImpl;

  @override
  bool get showSmartInvestmentPurchasePercentage;
  @override
  bool get useStaticPurchasePercentage;
  @override
  bool get showPurchaseProductFields;
  @override
  bool get showBenefitRules;
  @override
  List<PurchasePercentage> get staticPurchasePercentages;

  /// Create a copy of InvestmentFeatureConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvestmentFeatureConfigImplCopyWith<_$InvestmentFeatureConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
