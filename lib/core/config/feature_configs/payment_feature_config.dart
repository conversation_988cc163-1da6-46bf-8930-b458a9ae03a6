import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../features/wallet/deposit/domain/models/wallet_coin/wallet_coin.dart';
import '../../constants/assets.dart';
import '../../constants/enums.dart';
import '../../models/app_config/payment_type_model.dart';

part 'payment_feature_config.freezed.dart';

/// Configuration for payment-related features
/// 
/// This configuration handles payment types, wallet settings, and payment-related
/// feature flags. It supports both static and API-based payment configurations.
@freezed
class PaymentFeatureConfig with _$PaymentFeatureConfig {
  const factory PaymentFeatureConfig({
    required bool useStaticPaymentTypes,
    required bool shouldFetchWalletCoinsFromApi,
    required bool showAddWalletAddress,
    required bool showDepositeTxHash,
    required List<PaymentTypeModel> staticPaymentTypes,
    required List<PaymentTypeModel> staticWithdrawAddressTypes,
  }) = _PaymentFeatureConfig;

  /// Creates a static payment configuration (for sf_app, cfroex, ncm flavors)
  factory PaymentFeatureConfig.static() => PaymentFeatureConfig(
        useStaticPaymentTypes: true,
        shouldFetchWalletCoinsFromApi: false,
        showAddWalletAddress: false,
        showDepositeTxHash: false,
        staticPaymentTypes: _defaultStaticPaymentTypes,
        staticWithdrawAddressTypes: _defaultStaticWithdrawAddressTypes,
      );

  /// Creates an API-based payment configuration (for sis flavor)
  factory PaymentFeatureConfig.api({
    bool showAddWalletAddress = false,
    bool showDepositeTxHash = true,
  }) =>
      PaymentFeatureConfig(
        useStaticPaymentTypes: false,
        shouldFetchWalletCoinsFromApi: true,
        showAddWalletAddress: showAddWalletAddress,
        showDepositeTxHash: showDepositeTxHash,
        staticPaymentTypes: _defaultStaticPaymentTypes, // fallback
        staticWithdrawAddressTypes: _defaultStaticWithdrawAddressTypes,
      );

  /// Default static payment types for non-API flavors
  static const List<PaymentTypeModel> _defaultStaticPaymentTypes = [
    PaymentTypeModel(
      code: 'TRC20',
      name: 'TRC20 (USDT)',
      networkType: PaymentType.TRC20,
      icon: Assets.trcIcon,
      id: 1,
    ),
    PaymentTypeModel(
      code: 'ERC20',
      name: 'ERC20 (USDT)',
      networkType: PaymentType.ERC20,
      icon: Assets.ercIcon,
      id: 2,
    ),
  ];

  /// Static payment types for withdraw address management
  static const List<PaymentTypeModel> _defaultStaticWithdrawAddressTypes = [
    PaymentTypeModel(
      code: 'TRC20_USDT',
      name: 'TRC20 (USDT)',
      networkType: PaymentType.TRC20,
      icon: Assets.trcIcon,
      id: 1,
    ),
    PaymentTypeModel(
      code: 'ERC20_USDT',
      name: 'ERC20 (USDT)',
      networkType: PaymentType.ERC20,
      icon: Assets.ercIcon,
      id: 2,
    ),
    PaymentTypeModel(
      code: 'TRC20_USDC',
      name: 'TRC20 (USDC)',
      networkType: PaymentType.TRC20,
      icon: Assets.trcIcon,
      id: 3,
    ),
    PaymentTypeModel(
      code: 'ERC20_USDC',
      name: 'ERC20 (USDC)',
      networkType: PaymentType.ERC20,
      icon: Assets.ercIcon,
      id: 4,
    ),
  ];
}

/// Extension methods for payment configuration logic
extension PaymentFeatureConfigExtension on PaymentFeatureConfig {
  /// Gets available payment types based on configuration
  List<PaymentTypeModel> getAvailablePaymentTypes({
    List<WalletCoinData>? apiWalletCoins,
  }) {
    if (useStaticPaymentTypes) return staticPaymentTypes;
    
    final hasApiData = apiWalletCoins?.isNotEmpty ?? false;
    return hasApiData
        ? apiWalletCoins!.map(PaymentTypeModel.fromWalletCoin).toList()
        : staticPaymentTypes; // fallback
  }

  /// Gets default payment type
  PaymentTypeModel? getDefaultPaymentType({
    List<WalletCoinData>? apiWalletCoins,
  }) {
    final availableTypes = getAvailablePaymentTypes(apiWalletCoins: apiWalletCoins);
    return availableTypes.isNotEmpty ? availableTypes.first : null;
  }

  /// Converts payment code to PaymentType enum
  PaymentType getPaymentTypeFromCode(String paymentCode) {
    return paymentCode.contains('ERC20')
        ? PaymentType.ERC20
        : PaymentType.TRC20;
  }
}
