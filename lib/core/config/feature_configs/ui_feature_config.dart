import 'package:freezed_annotation/freezed_annotation.dart';
import '../../constants/enums.dart';

part 'ui_feature_config.freezed.dart';

/// Configuration for UI-related features and display settings
/// 
/// This configuration handles UI components, visual settings, debug flags,
/// and other user interface related configurations.
@freezed
class UiFeatureConfig with _$UiFeatureConfig {
  const factory UiFeatureConfig({
    required bool showDebugVersionTag,
    required bool showTradingWallet,
    required bool showTransferPreview,
    required bool showTransfer,
    required bool showMentorVipLevel,
    required bool showWithdrawHistory,
    required bool showAppUpdate,
    required MarketColor marketColor,
    required bool disableUpperCasePasswordProtection,
    required bool fetchCommunityRecords,
  }) = _UiFeatureConfig;

  /// Creates a standard UI configuration for most flavors
  factory UiFeatureConfig.standard({
    bool showDebugVersionTag = true,
    bool showTradingWallet = true,
    bool showTransferPreview = true,
    bool showTransfer = true,
    bool showMentorVipLevel = false,
    bool showWithdrawHistory = false,
    bool showAppUpdate = false,
    MarketColor marketColor = MarketColor.greenUpRedDown,
    bool disableUpperCasePasswordProtection = false,
    bool fetchCommunityRecords = true,
  }) =>
      UiFeatureConfig(
        showDebugVersionTag: showDebugVersionTag,
        showTradingWallet: showTradingWallet,
        showTransferPreview: showTransferPreview,
        showTransfer: showTransfer,
        showMentorVipLevel: showMentorVipLevel,
        showWithdrawHistory: showWithdrawHistory,
        showAppUpdate: showAppUpdate,
        marketColor: marketColor,
        disableUpperCasePasswordProtection: disableUpperCasePasswordProtection,
        fetchCommunityRecords: fetchCommunityRecords,
      );

  /// Creates a minimal UI configuration for basic flavors
  factory UiFeatureConfig.minimal({
    bool showDebugVersionTag = false,
    bool showTradingWallet = false,
    bool showTransferPreview = false,
    bool showTransfer = false,
    bool showMentorVipLevel = true,
    bool showWithdrawHistory = false,
    bool showAppUpdate = true,
    MarketColor marketColor = MarketColor.greenUpRedDown,
    bool disableUpperCasePasswordProtection = false,
    bool fetchCommunityRecords = false,
  }) =>
      UiFeatureConfig(
        showDebugVersionTag: showDebugVersionTag,
        showTradingWallet: showTradingWallet,
        showTransferPreview: showTransferPreview,
        showTransfer: showTransfer,
        showMentorVipLevel: showMentorVipLevel,
        showWithdrawHistory: showWithdrawHistory,
        showAppUpdate: showAppUpdate,
        marketColor: marketColor,
        disableUpperCasePasswordProtection: disableUpperCasePasswordProtection,
        fetchCommunityRecords: fetchCommunityRecords,
      );

  /// Creates an enhanced UI configuration for premium flavors
  factory UiFeatureConfig.enhanced({
    bool showDebugVersionTag = true,
    bool showTradingWallet = true,
    bool showTransferPreview = true,
    bool showTransfer = true,
    bool showMentorVipLevel = true,
    bool showWithdrawHistory = true,
    bool showAppUpdate = true,
    MarketColor marketColor = MarketColor.greenUpRedDown,
    bool disableUpperCasePasswordProtection = false,
    bool fetchCommunityRecords = true,
  }) =>
      UiFeatureConfig(
        showDebugVersionTag: showDebugVersionTag,
        showTradingWallet: showTradingWallet,
        showTransferPreview: showTransferPreview,
        showTransfer: showTransfer,
        showMentorVipLevel: showMentorVipLevel,
        showWithdrawHistory: showWithdrawHistory,
        showAppUpdate: showAppUpdate,
        marketColor: marketColor,
        disableUpperCasePasswordProtection: disableUpperCasePasswordProtection,
        fetchCommunityRecords: fetchCommunityRecords,
      );
}
