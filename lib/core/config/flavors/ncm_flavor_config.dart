import 'package:flutter/material.dart';
import '../../../flavors.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import 'base_flavor_config.dart';

/// NCM flavor configuration
/// 
/// This class defines the complete configuration for the NCM app flavor,
/// which currently uses the same configuration as Super Future but can
/// be customized independently in the future.
class NcmFlavorConfig {
  /// Creates the NCM flavor configuration
  static FlavorConfig create(bool isDebug) => FlavorConfig.base(
        flavor: Flavor.ncm,
        appName: 'NCM Financial',
        baseUrl: isDebug
            ? 'https://api.superfuture.world'
            : 'https://api.superfuture.world', // Same as SF for now
        marketWsUrl: isDebug
            ? 'wss://api.superfuture.world/ws'
            : 'wss://api.superfuture.world/ws',
        appUrl: 'https://superfuture.world/', // Same as SF for now
        iconPath: 'assets/logo/ncm/logo.svg',
        introVideoPath: 'assets/splash/ncm/introVideo.mp4',
        accountType: '3',
        defaultLocale: const Locale('en', 'US'),
        paymentConfig: _createPaymentConfig(),
        investmentConfig: _createInvestmentConfig(),
        uiConfig: _createUiConfig(),
      );

  /// Creates payment configuration for NCM flavor (static)
  static PaymentFeatureConfig _createPaymentConfig() =>
      PaymentFeatureConfig.static();

  /// Creates investment configuration for NCM flavor (static)
  static InvestmentFeatureConfig _createInvestmentConfig() =>
      InvestmentFeatureConfig.static(
        showSmartInvestmentPurchasePercentage: true,
        showPurchaseProductFields: false,
        showBenefitRules: false,
      );

  /// Creates UI configuration for NCM flavor (standard features)
  static UiFeatureConfig _createUiConfig() => UiFeatureConfig.standard(
        showDebugVersionTag: true,
        showTradingWallet: true,
        showTransferPreview: true,
        showTransfer: true,
        showMentorVipLevel: false,
        showWithdrawHistory: false,
        showAppUpdate: false,
        fetchCommunityRecords: true,
      );
}
