import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../flavors.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';

part 'base_flavor_config.freezed.dart';

/// Base flavor configuration that defines the structure for all app flavors
/// 
/// This configuration combines app metadata with feature-specific configurations
/// to provide a complete flavor definition. Each flavor extends this base.
@freezed
class FlavorConfig with _$FlavorConfig {
  const factory FlavorConfig({
    required Flavor flavor,
    required String appName,
    required String baseUrl,
    required String marketWsUrl,
    required String appUrl,
    required String iconPath,
    required String introVideoPath,
    required String accountType,
    required Locale defaultLocale,
    required PaymentFeatureConfig paymentConfig,
    required InvestmentFeatureConfig investmentConfig,
    required UiFeatureConfig uiConfig,
  }) = _FlavorConfig;

  /// Creates a base flavor configuration with common defaults
  factory FlavorConfig.base({
    required Flavor flavor,
    required String appName,
    required String baseUrl,
    required String marketWsUrl,
    required String appUrl,
    required String iconPath,
    required String introVideoPath,
    required String accountType,
    required Locale defaultLocale,
    PaymentFeatureConfig? paymentConfig,
    InvestmentFeatureConfig? investmentConfig,
    UiFeatureConfig? uiConfig,
  }) =>
      FlavorConfig(
        flavor: flavor,
        appName: appName,
        baseUrl: baseUrl,
        marketWsUrl: marketWsUrl,
        appUrl: appUrl,
        iconPath: iconPath,
        introVideoPath: introVideoPath,
        accountType: accountType,
        defaultLocale: defaultLocale,
        paymentConfig: paymentConfig ?? PaymentFeatureConfig.static(),
        investmentConfig: investmentConfig ?? InvestmentFeatureConfig.static(),
        uiConfig: uiConfig ?? UiFeatureConfig.standard(),
      );
}

/// Extension methods for flavor configuration convenience
extension FlavorConfigExtension on FlavorConfig {
  /// Gets the flavor name as a string
  String get flavorName => flavor.name;

  /// Gets the display title for the flavor
  String get flavorTitle => appName;

  /// Determines URLs based on debug mode
  String getBaseUrl(bool isDebug) => baseUrl;
  String getMarketWsUrl(bool isDebug) => marketWsUrl;
}
