// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'base_flavor_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FlavorConfig {
  Flavor get flavor => throw _privateConstructorUsedError;
  String get appName => throw _privateConstructorUsedError;
  String get baseUrl => throw _privateConstructorUsedError;
  String get marketWsUrl => throw _privateConstructorUsedError;
  String get appUrl => throw _privateConstructorUsedError;
  String get iconPath => throw _privateConstructorUsedError;
  String get introVideoPath => throw _privateConstructorUsedError;
  String get accountType => throw _privateConstructorUsedError;
  Locale get defaultLocale => throw _privateConstructorUsedError;
  PaymentFeatureConfig get paymentConfig => throw _privateConstructorUsedError;
  InvestmentFeatureConfig get investmentConfig =>
      throw _privateConstructorUsedError;
  UiFeatureConfig get uiConfig => throw _privateConstructorUsedError;

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FlavorConfigCopyWith<FlavorConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlavorConfigCopyWith<$Res> {
  factory $FlavorConfigCopyWith(
          FlavorConfig value, $Res Function(FlavorConfig) then) =
      _$FlavorConfigCopyWithImpl<$Res, FlavorConfig>;
  @useResult
  $Res call(
      {Flavor flavor,
      String appName,
      String baseUrl,
      String marketWsUrl,
      String appUrl,
      String iconPath,
      String introVideoPath,
      String accountType,
      Locale defaultLocale,
      PaymentFeatureConfig paymentConfig,
      InvestmentFeatureConfig investmentConfig,
      UiFeatureConfig uiConfig});

  $PaymentFeatureConfigCopyWith<$Res> get paymentConfig;
  $InvestmentFeatureConfigCopyWith<$Res> get investmentConfig;
  $UiFeatureConfigCopyWith<$Res> get uiConfig;
}

/// @nodoc
class _$FlavorConfigCopyWithImpl<$Res, $Val extends FlavorConfig>
    implements $FlavorConfigCopyWith<$Res> {
  _$FlavorConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flavor = null,
    Object? appName = null,
    Object? baseUrl = null,
    Object? marketWsUrl = null,
    Object? appUrl = null,
    Object? iconPath = null,
    Object? introVideoPath = null,
    Object? accountType = null,
    Object? defaultLocale = null,
    Object? paymentConfig = null,
    Object? investmentConfig = null,
    Object? uiConfig = null,
  }) {
    return _then(_value.copyWith(
      flavor: null == flavor
          ? _value.flavor
          : flavor // ignore: cast_nullable_to_non_nullable
              as Flavor,
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      baseUrl: null == baseUrl
          ? _value.baseUrl
          : baseUrl // ignore: cast_nullable_to_non_nullable
              as String,
      marketWsUrl: null == marketWsUrl
          ? _value.marketWsUrl
          : marketWsUrl // ignore: cast_nullable_to_non_nullable
              as String,
      appUrl: null == appUrl
          ? _value.appUrl
          : appUrl // ignore: cast_nullable_to_non_nullable
              as String,
      iconPath: null == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String,
      introVideoPath: null == introVideoPath
          ? _value.introVideoPath
          : introVideoPath // ignore: cast_nullable_to_non_nullable
              as String,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String,
      defaultLocale: null == defaultLocale
          ? _value.defaultLocale
          : defaultLocale // ignore: cast_nullable_to_non_nullable
              as Locale,
      paymentConfig: null == paymentConfig
          ? _value.paymentConfig
          : paymentConfig // ignore: cast_nullable_to_non_nullable
              as PaymentFeatureConfig,
      investmentConfig: null == investmentConfig
          ? _value.investmentConfig
          : investmentConfig // ignore: cast_nullable_to_non_nullable
              as InvestmentFeatureConfig,
      uiConfig: null == uiConfig
          ? _value.uiConfig
          : uiConfig // ignore: cast_nullable_to_non_nullable
              as UiFeatureConfig,
    ) as $Val);
  }

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentFeatureConfigCopyWith<$Res> get paymentConfig {
    return $PaymentFeatureConfigCopyWith<$Res>(_value.paymentConfig, (value) {
      return _then(_value.copyWith(paymentConfig: value) as $Val);
    });
  }

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InvestmentFeatureConfigCopyWith<$Res> get investmentConfig {
    return $InvestmentFeatureConfigCopyWith<$Res>(_value.investmentConfig,
        (value) {
      return _then(_value.copyWith(investmentConfig: value) as $Val);
    });
  }

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UiFeatureConfigCopyWith<$Res> get uiConfig {
    return $UiFeatureConfigCopyWith<$Res>(_value.uiConfig, (value) {
      return _then(_value.copyWith(uiConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FlavorConfigImplCopyWith<$Res>
    implements $FlavorConfigCopyWith<$Res> {
  factory _$$FlavorConfigImplCopyWith(
          _$FlavorConfigImpl value, $Res Function(_$FlavorConfigImpl) then) =
      __$$FlavorConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Flavor flavor,
      String appName,
      String baseUrl,
      String marketWsUrl,
      String appUrl,
      String iconPath,
      String introVideoPath,
      String accountType,
      Locale defaultLocale,
      PaymentFeatureConfig paymentConfig,
      InvestmentFeatureConfig investmentConfig,
      UiFeatureConfig uiConfig});

  @override
  $PaymentFeatureConfigCopyWith<$Res> get paymentConfig;
  @override
  $InvestmentFeatureConfigCopyWith<$Res> get investmentConfig;
  @override
  $UiFeatureConfigCopyWith<$Res> get uiConfig;
}

/// @nodoc
class __$$FlavorConfigImplCopyWithImpl<$Res>
    extends _$FlavorConfigCopyWithImpl<$Res, _$FlavorConfigImpl>
    implements _$$FlavorConfigImplCopyWith<$Res> {
  __$$FlavorConfigImplCopyWithImpl(
      _$FlavorConfigImpl _value, $Res Function(_$FlavorConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flavor = null,
    Object? appName = null,
    Object? baseUrl = null,
    Object? marketWsUrl = null,
    Object? appUrl = null,
    Object? iconPath = null,
    Object? introVideoPath = null,
    Object? accountType = null,
    Object? defaultLocale = null,
    Object? paymentConfig = null,
    Object? investmentConfig = null,
    Object? uiConfig = null,
  }) {
    return _then(_$FlavorConfigImpl(
      flavor: null == flavor
          ? _value.flavor
          : flavor // ignore: cast_nullable_to_non_nullable
              as Flavor,
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      baseUrl: null == baseUrl
          ? _value.baseUrl
          : baseUrl // ignore: cast_nullable_to_non_nullable
              as String,
      marketWsUrl: null == marketWsUrl
          ? _value.marketWsUrl
          : marketWsUrl // ignore: cast_nullable_to_non_nullable
              as String,
      appUrl: null == appUrl
          ? _value.appUrl
          : appUrl // ignore: cast_nullable_to_non_nullable
              as String,
      iconPath: null == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String,
      introVideoPath: null == introVideoPath
          ? _value.introVideoPath
          : introVideoPath // ignore: cast_nullable_to_non_nullable
              as String,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String,
      defaultLocale: null == defaultLocale
          ? _value.defaultLocale
          : defaultLocale // ignore: cast_nullable_to_non_nullable
              as Locale,
      paymentConfig: null == paymentConfig
          ? _value.paymentConfig
          : paymentConfig // ignore: cast_nullable_to_non_nullable
              as PaymentFeatureConfig,
      investmentConfig: null == investmentConfig
          ? _value.investmentConfig
          : investmentConfig // ignore: cast_nullable_to_non_nullable
              as InvestmentFeatureConfig,
      uiConfig: null == uiConfig
          ? _value.uiConfig
          : uiConfig // ignore: cast_nullable_to_non_nullable
              as UiFeatureConfig,
    ));
  }
}

/// @nodoc

class _$FlavorConfigImpl implements _FlavorConfig {
  const _$FlavorConfigImpl(
      {required this.flavor,
      required this.appName,
      required this.baseUrl,
      required this.marketWsUrl,
      required this.appUrl,
      required this.iconPath,
      required this.introVideoPath,
      required this.accountType,
      required this.defaultLocale,
      required this.paymentConfig,
      required this.investmentConfig,
      required this.uiConfig});

  @override
  final Flavor flavor;
  @override
  final String appName;
  @override
  final String baseUrl;
  @override
  final String marketWsUrl;
  @override
  final String appUrl;
  @override
  final String iconPath;
  @override
  final String introVideoPath;
  @override
  final String accountType;
  @override
  final Locale defaultLocale;
  @override
  final PaymentFeatureConfig paymentConfig;
  @override
  final InvestmentFeatureConfig investmentConfig;
  @override
  final UiFeatureConfig uiConfig;

  @override
  String toString() {
    return 'FlavorConfig(flavor: $flavor, appName: $appName, baseUrl: $baseUrl, marketWsUrl: $marketWsUrl, appUrl: $appUrl, iconPath: $iconPath, introVideoPath: $introVideoPath, accountType: $accountType, defaultLocale: $defaultLocale, paymentConfig: $paymentConfig, investmentConfig: $investmentConfig, uiConfig: $uiConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlavorConfigImpl &&
            (identical(other.flavor, flavor) || other.flavor == flavor) &&
            (identical(other.appName, appName) || other.appName == appName) &&
            (identical(other.baseUrl, baseUrl) || other.baseUrl == baseUrl) &&
            (identical(other.marketWsUrl, marketWsUrl) ||
                other.marketWsUrl == marketWsUrl) &&
            (identical(other.appUrl, appUrl) || other.appUrl == appUrl) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            (identical(other.introVideoPath, introVideoPath) ||
                other.introVideoPath == introVideoPath) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.defaultLocale, defaultLocale) ||
                other.defaultLocale == defaultLocale) &&
            (identical(other.paymentConfig, paymentConfig) ||
                other.paymentConfig == paymentConfig) &&
            (identical(other.investmentConfig, investmentConfig) ||
                other.investmentConfig == investmentConfig) &&
            (identical(other.uiConfig, uiConfig) ||
                other.uiConfig == uiConfig));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      flavor,
      appName,
      baseUrl,
      marketWsUrl,
      appUrl,
      iconPath,
      introVideoPath,
      accountType,
      defaultLocale,
      paymentConfig,
      investmentConfig,
      uiConfig);

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FlavorConfigImplCopyWith<_$FlavorConfigImpl> get copyWith =>
      __$$FlavorConfigImplCopyWithImpl<_$FlavorConfigImpl>(this, _$identity);
}

abstract class _FlavorConfig implements FlavorConfig {
  const factory _FlavorConfig(
      {required final Flavor flavor,
      required final String appName,
      required final String baseUrl,
      required final String marketWsUrl,
      required final String appUrl,
      required final String iconPath,
      required final String introVideoPath,
      required final String accountType,
      required final Locale defaultLocale,
      required final PaymentFeatureConfig paymentConfig,
      required final InvestmentFeatureConfig investmentConfig,
      required final UiFeatureConfig uiConfig}) = _$FlavorConfigImpl;

  @override
  Flavor get flavor;
  @override
  String get appName;
  @override
  String get baseUrl;
  @override
  String get marketWsUrl;
  @override
  String get appUrl;
  @override
  String get iconPath;
  @override
  String get introVideoPath;
  @override
  String get accountType;
  @override
  Locale get defaultLocale;
  @override
  PaymentFeatureConfig get paymentConfig;
  @override
  InvestmentFeatureConfig get investmentConfig;
  @override
  UiFeatureConfig get uiConfig;

  /// Create a copy of FlavorConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FlavorConfigImplCopyWith<_$FlavorConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
