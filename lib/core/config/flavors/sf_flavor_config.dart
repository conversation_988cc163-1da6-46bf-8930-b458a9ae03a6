import 'package:flutter/material.dart';
import '../../../flavors.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import 'base_flavor_config.dart';

/// Super Future flavor configuration
/// 
/// This class defines the complete configuration for the Super Future app flavor,
/// including app metadata, URLs, assets, and feature configurations.
class SfFlavorConfig {
  /// Creates the Super Future flavor configuration
  static FlavorConfig create() => FlavorConfig.base(
        flavor: Flavor.sf_app,
        appName: 'Super Future',
        baseUrl: 'https://api.superfuture.world',
        marketWsUrl: 'wss://api.superfuture.world/ws',
        appUrl: 'https://superfuture.world/',
        iconPath: 'assets/logo/sf_app/logo.svg',
        introVideoPath: 'assets/splash/sf_app/introVideo.mp4',
        accountType: '3',
        defaultLocale: const Locale('en', 'US'),
        paymentConfig: _createPaymentConfig(),
        investmentConfig: _createInvestmentConfig(),
        uiConfig: _createUiConfig(),
      );

  /// Creates payment configuration for Super Future flavor
  static PaymentFeatureConfig _createPaymentConfig() =>
      PaymentFeatureConfig.static();

  /// Creates investment configuration for Super Future flavor
  static InvestmentFeatureConfig _createInvestmentConfig() =>
      InvestmentFeatureConfig.static(
        showSmartInvestmentPurchasePercentage: true,
        showPurchaseProductFields: false,
        showBenefitRules: false,
      );

  /// Creates UI configuration for Super Future flavor
  static UiFeatureConfig _createUiConfig() => UiFeatureConfig.standard(
        showDebugVersionTag: true,
        showTradingWallet: true,
        showTransferPreview: true,
        showTransfer: true,
        showMentorVipLevel: false,
        showWithdrawHistory: false,
        showAppUpdate: false,
        fetchCommunityRecords: true,
      );
}
