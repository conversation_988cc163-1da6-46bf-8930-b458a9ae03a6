import 'package:flutter/material.dart';
import '../../../flavors.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import 'base_flavor_config.dart';

/// SIS flavor configuration
/// 
/// This class defines the complete configuration for the SIS app flavor,
/// which uses API-based configurations and has different feature sets.
class SisFlavorConfig {
  /// Creates the SIS flavor configuration
  static FlavorConfig create() => FlavorConfig.base(
        flavor: Flavor.sis,
        appName: 'SIS',
        baseUrl: 'https://api.sisinvestors.com',
        marketWsUrl: 'wss://api.sisinvestors.com/ws',
        appUrl: 'https://sisinvestors.com/',
        iconPath: 'assets/logo/sis/logo.svg',
        introVideoPath: 'assets/splash/sis/introVideo.mp4',
        accountType: '1',
        defaultLocale: const Locale('en', 'US'),
        paymentConfig: _createPaymentConfig(),
        investmentConfig: _createInvestmentConfig(),
        uiConfig: _createUiConfig(),
      );

  /// Creates payment configuration for SIS flavor (API-based)
  static PaymentFeatureConfig _createPaymentConfig() =>
      PaymentFeatureConfig.api(
        showAddWalletAddress: false,
        showDepositeTxHash: true,
      );

  /// Creates investment configuration for SIS flavor (API-based)
  static InvestmentFeatureConfig _createInvestmentConfig() =>
      InvestmentFeatureConfig.api(
        showSmartInvestmentPurchasePercentage: true,
        showPurchaseProductFields: true,
        showBenefitRules: true,
      );

  /// Creates UI configuration for SIS flavor (minimal features)
  static UiFeatureConfig _createUiConfig() => UiFeatureConfig.minimal(
        showDebugVersionTag: false,
        showTradingWallet: false,
        showTransferPreview: false,
        showTransfer: false,
        showMentorVipLevel: true,
        showWithdrawHistory: false,
        showAppUpdate: true,
        fetchCommunityRecords: false,
      );
}
