import 'package:flutter/material.dart';
import '../../../flavors.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import 'base_flavor_config.dart';

/// CFROEX (Forex Fusion) flavor configuration
/// 
/// This class defines the complete configuration for the CFROEX app flavor,
/// which uses static configurations and has Spanish as default language.
class CfroexFlavorConfig {
  /// Creates the CFROEX flavor configuration
  static FlavorConfig create(bool isDebug) => FlavorConfig.base(
        flavor: Flavor.cfroex,
        appName: 'Forex Fusion',
        baseUrl: isDebug
            ? 'https://api.superfuture.world'
            : 'https://api.chalanforex.com',
        marketWsUrl: isDebug
            ? 'wss://api.superfuture.world/ws'
            : 'wss://api.chalanforex.com/ws',
        appUrl: 'https://chalanforex.com/',
        iconPath: 'assets/logo/cfroex/logo.svg',
        introVideoPath: 'assets/splash/cfroex/introVideo.mp4',
        accountType: '3',
        defaultLocale: const Locale('es', 'ES'), // Spanish default
        paymentConfig: _createPaymentConfig(),
        investmentConfig: _createInvestmentConfig(),
        uiConfig: _createUiConfig(),
      );

  /// Creates payment configuration for CFROEX flavor (static)
  static PaymentFeatureConfig _createPaymentConfig() =>
      PaymentFeatureConfig.static();

  /// Creates investment configuration for CFROEX flavor (static)
  static InvestmentFeatureConfig _createInvestmentConfig() =>
      InvestmentFeatureConfig.static(
        showSmartInvestmentPurchasePercentage: true,
        showPurchaseProductFields: false,
        showBenefitRules: false,
      );

  /// Creates UI configuration for CFROEX flavor (enhanced features)
  static UiFeatureConfig _createUiConfig() => UiFeatureConfig.enhanced(
        showDebugVersionTag: false,
        showTradingWallet: true,
        showTransferPreview: true,
        showTransfer: true,
        showMentorVipLevel: false,
        showWithdrawHistory: true, // Enabled for CFROEX
        showAppUpdate: true,
        fetchCommunityRecords: true,
        disableUpperCasePasswordProtection: true, // Disabled for CFROEX
      );
}
