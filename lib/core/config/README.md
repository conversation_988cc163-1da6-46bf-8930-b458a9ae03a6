# Configuration System Documentation

## Overview

The new configuration system provides a flexible, multi-layered approach to managing app settings across different environments and flavors. It uses dependency injection to ensure configurations are available throughout the app while maintaining clean separation of concerns.

## Architecture

```
ConfigModule (DI Registration)
├── EnvironmentConfig (Debug/Production)
├── FlavorConfig (App Variants)
│   ├── PaymentFeatureConfig
│   ├── InvestmentFeatureConfig
│   └── UiFeatureConfig
```

## Key Benefits

### ✅ **Improved Testability**
- Easy to mock configurations in unit tests
- No global singleton dependencies
- Clean dependency injection

### ✅ **Better Separation of Concerns**
- Payment logic separated from UI logic
- Feature-specific configurations
- Environment-specific settings isolated

### ✅ **Enhanced Maintainability**
- Modular configuration structure
- Easy to add new flavors
- Clear configuration inheritance

### ✅ **Type Safety**
- Immutable configurations with Freezed
- Compile-time configuration validation
- No runtime configuration errors

## Core Components

### 1. ConfigModule (`config_module.dart`)
Central dependency injection module that registers all configuration providers.

### 2. EnvironmentConfig (`environment_config.dart`)
Automatically switches between debug and production settings based on build mode.

### 3. FlavorConfig (`flavors/base_flavor_config.dart`)
Defines app variants with different branding, URLs, and feature sets.

### 4. Feature Configurations
- **PaymentFeatureConfig**: Payment types, wallet settings, API vs static modes
- **InvestmentFeatureConfig**: Investment features, purchase percentages
- **UiFeatureConfig**: UI components, market colors, debug flags

## Current Implementation

### Super Future Flavor (Default)
```dart
// Currently hardcoded in ConfigModule
@singleton
FlavorConfig get flavorConfig => SfFlavorConfig.create();
```

**Configuration:**
- Static payment types (TRC20, ERC20)
- Static purchase percentages (30%, 50%, 100%)
- Standard UI features enabled
- Trading wallet and transfer features enabled

## Usage Examples

### Accessing Configurations
```dart
// Get specific feature configurations
final paymentConfig = getIt<PaymentFeatureConfig>();
final investmentConfig = getIt<InvestmentFeatureConfig>();
final uiConfig = getIt<UiFeatureConfig>();
final flavorConfig = getIt<FlavorConfig>();

// Use in widgets
if (uiConfig.showTradingWallet) {
  return TradingWalletWidget();
}

// Use in business logic
final paymentTypes = paymentConfig.getAvailablePaymentTypes(
  apiWalletCoins: apiData,
);
```

### BLoC Integration
```dart
class SmartInvestmentCubit extends Cubit<SmartInvestmentState> {
  final InvestmentFeatureConfig _investmentConfig;
  
  SmartInvestmentCubit(this._investmentConfig) : super(initialState);
  
  void initializePercentages() {
    final percentages = _investmentConfig.getPurchasePercentageList();
    emit(state.copyWith(percentages: percentages));
  }
}
```

## Adding New Flavors

### Step 1: Create Flavor Configuration
```dart
// lib/core/config/flavors/new_flavor_config.dart
class NewFlavorConfig {
  static FlavorConfig create() => FlavorConfig.base(
    flavor: Flavor.newFlavor,
    appName: 'New App Name',
    baseUrl: 'https://api.newapp.com',
    paymentConfig: PaymentFeatureConfig.api(),
    investmentConfig: InvestmentFeatureConfig.static(),
    uiConfig: UiFeatureConfig.enhanced(),
  );
}
```

### Step 2: Update ConfigModule
```dart
@singleton
FlavorConfig get flavorConfig {
  const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'sf');
  
  switch (flavor) {
    case 'new_flavor':
      return NewFlavorConfig.create();
    case 'sf':
    default:
      return SfFlavorConfig.create();
  }
}
```

### Step 3: Build with Flavor
```bash
flutter run --dart-define=FLAVOR=new_flavor
```

## Migration from Old System

### Before (Singleton Pattern)
```dart
// Hard to test, tightly coupled
final config = AppConfig.instance;
final paymentTypes = AppConfig.getAvailablePaymentTypes();
```

### After (Dependency Injection)
```dart
// Easy to test, loosely coupled
final paymentConfig = getIt<PaymentFeatureConfig>();
final paymentTypes = paymentConfig.getAvailablePaymentTypes();
```

## File Structure

```
lib/core/config/
├── config_module.dart                    # DI registration
├── environment_config.dart               # Environment settings
├── feature_configs/
│   ├── payment_feature_config.dart       # Payment configuration
│   ├── investment_feature_config.dart    # Investment configuration
│   └── ui_feature_config.dart           # UI configuration
├── flavors/
│   ├── base_flavor_config.dart          # Base flavor interface
│   ├── sf_flavor_config.dart            # Super Future flavor
│   └── sis_flavor_config.dart           # SIS flavor (example)
├── examples/
│   └── usage_examples.dart              # Usage examples
├── MIGRATION_GUIDE.md                   # Migration instructions
└── README.md                           # This file
```

## Testing

### Unit Testing
```dart
testWidgets('should show trading wallet when enabled', (tester) async {
  // Arrange
  final mockUiConfig = MockUiFeatureConfig();
  when(mockUiConfig.showTradingWallet).thenReturn(true);
  getIt.registerSingleton<UiFeatureConfig>(mockUiConfig);
  
  // Act
  await tester.pumpWidget(MyWidget());
  
  // Assert
  expect(find.byType(TradingWalletWidget), findsOneWidget);
});
```

### Integration Testing
```dart
void main() {
  group('Configuration Integration Tests', () {
    setUp(() {
      // Register real configurations for integration tests
      getIt.registerSingleton<FlavorConfig>(SfFlavorConfig.create());
    });
    
    tearDown(() {
      getIt.reset();
    });
    
    test('should provide consistent configuration', () {
      final config = getIt<FlavorConfig>();
      expect(config.appName, 'Super Future');
      expect(config.paymentConfig.useStaticPaymentTypes, true);
    });
  });
}
```

## Best Practices

1. **Single Responsibility**: Each config handles one concern
2. **Immutable**: Use `@freezed` for immutable configurations
3. **Default Values**: Provide sensible defaults for all settings
4. **Environment Separation**: Keep debug/production settings separate
5. **Feature Flags**: Use boolean flags for feature toggles
6. **Asset Organization**: Group assets by flavor for easy management

## Future Enhancements

1. **Dynamic Flavor Selection**: Currently hardcoded to SF flavor
2. **Runtime Configuration**: Support for server-side configuration updates
3. **A/B Testing**: Integration with feature flag services
4. **Configuration Validation**: Compile-time validation of configuration consistency

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all configurations are registered in ConfigModule
2. **Circular Dependencies**: Avoid configurations depending on each other
3. **Test Setup**: Remember to register mock configurations in tests

### Debug Tips

```dart
// Check current configuration
final config = getIt<FlavorConfig>();
print('Current flavor: ${config.flavorName}');
print('Payment config: ${config.paymentConfig}');
```
