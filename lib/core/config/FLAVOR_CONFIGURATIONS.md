# Flavor Configurations Overview

## All Flavors Implemented

The configuration system now supports all four app flavors with their unique settings and features.

## Flavor Comparison

| Feature | SF (Super Future) | SIS | CFROEX (Forex Fusion) | NCM Financial |
|---------|-------------------|-----|------------------------|---------------|
| **App Name** | Super Future | SIS | Forex Fusion | NCM Financial |
| **Account Type** | 3 | 1 | 3 | 3 |
| **Default Language** | English (US) | English (US) | Spanish (ES) | English (US) |
| **Payment Strategy** | Static | API | Static | Static |
| **Investment Strategy** | Static | API | Static | Static |
| **UI Profile** | Standard | Minimal | Enhanced | Standard |

## Detailed Configuration Breakdown

### 🚀 Super Future (SF) - Default Flavor

**Build Command:** `flutter run --dart-define=FLAVOR=sf`

**Configuration:**
- **URLs:** `https://api.superfuture.world`
- **Assets:** `assets/logo/sf_app/`, `assets/splash/sf_app/`
- **Payment:** Static types (TRC20, ERC20)
- **Investment:** Static percentages (30%, 50%, 100%)
- **Features:**
  - ✅ Trading Wallet
  - ✅ Transfer & Transfer Preview
  - ✅ Smart Investment Purchase Percentage
  - ✅ Community Records
  - ❌ Benefit Rules
  - ❌ Mentor VIP Level
  - ❌ Withdraw History
  - ❌ App Update

### 🏦 SIS - API-Driven Flavor

**Build Command:** `flutter run --dart-define=FLAVOR=sis`

**Configuration:**
- **URLs:** 
  - Debug: `https://sis-api.superfuture.world`
  - Production: `https://api.sisinvestors.com`
- **Assets:** `assets/logo/sis/`, `assets/splash/sis/`
- **Payment:** API-based types (dynamic from server)
- **Investment:** API-based percentages (dynamic from server)
- **Features:**
  - ❌ Trading Wallet
  - ❌ Transfer & Transfer Preview
  - ✅ Smart Investment Purchase Percentage
  - ✅ Purchase Product Fields
  - ✅ Benefit Rules
  - ✅ Mentor VIP Level
  - ✅ Deposit TX Hash
  - ✅ App Update
  - ❌ Community Records

### 💱 CFROEX (Forex Fusion) - Enhanced Flavor

**Build Command:** `flutter run --dart-define=FLAVOR=cfroex`

**Configuration:**
- **URLs:**
  - Debug: `https://api.superfuture.world`
  - Production: `https://api.chalanforex.com`
- **Assets:** `assets/logo/cfroex/`, `assets/splash/cfroex/`
- **Payment:** Static types (TRC20, ERC20)
- **Investment:** Static percentages (30%, 50%, 100%)
- **Special Features:**
  - 🇪🇸 **Spanish Default Language**
  - ✅ **Disabled Upper Case Password Protection**
  - ✅ **Withdraw History Enabled**
- **Features:**
  - ✅ Trading Wallet
  - ✅ Transfer & Transfer Preview
  - ✅ Smart Investment Purchase Percentage
  - ✅ Community Records
  - ✅ Withdraw History
  - ✅ App Update
  - ❌ Benefit Rules
  - ❌ Mentor VIP Level

### 🏢 NCM Financial - Standard Flavor

**Build Command:** `flutter run --dart-define=FLAVOR=ncm`

**Configuration:**
- **URLs:** `https://api.superfuture.world` (same as SF for now)
- **Assets:** `assets/logo/ncm/`, `assets/splash/ncm/`
- **Payment:** Static types (TRC20, ERC20)
- **Investment:** Static percentages (30%, 50%, 100%)
- **Features:**
  - ✅ Trading Wallet
  - ✅ Transfer & Transfer Preview
  - ✅ Smart Investment Purchase Percentage
  - ✅ Community Records
  - ❌ Benefit Rules
  - ❌ Mentor VIP Level
  - ❌ Withdraw History
  - ❌ App Update

## Build Commands

### Development Builds
```bash
# Super Future (default)
flutter run --dart-define=FLAVOR=sf

# SIS
flutter run --dart-define=FLAVOR=sis

# CFROEX
flutter run --dart-define=FLAVOR=cfroex

# NCM
flutter run --dart-define=FLAVOR=ncm
```

### Production Builds
```bash
# Android APK
flutter build apk --dart-define=FLAVOR=sis --release

# iOS IPA
flutter build ipa --dart-define=FLAVOR=cfroex --release
```

## Configuration Access

All flavors use the same configuration access pattern:

```dart
// Get current flavor configuration
final flavorConfig = getIt<FlavorConfig>();
print('Current app: ${flavorConfig.appName}');
print('Current flavor: ${flavorConfig.flavorName}');

// Access feature configurations
final paymentConfig = getIt<PaymentFeatureConfig>();
final investmentConfig = getIt<InvestmentFeatureConfig>();
final uiConfig = getIt<UiFeatureConfig>();

// Check flavor-specific features
if (uiConfig.showTradingWallet) {
  // Show trading wallet (SF, CFROEX, NCM)
}

if (paymentConfig.shouldFetchWalletCoinsFromApi) {
  // Fetch from API (SIS only)
} else {
  // Use static types (SF, CFROEX, NCM)
}
```

## Asset Organization

Ensure your assets are organized by flavor:

```
assets/
├── logo/
│   ├── sf_app/
│   │   └── logo.svg
│   ├── sis/
│   │   └── logo.svg
│   ├── cfroex/
│   │   └── logo.svg
│   └── ncm/
│       └── logo.svg
└── splash/
    ├── sf_app/
    │   └── introVideo.mp4
    ├── sis/
    │   └── introVideo.mp4
    ├── cfroex/
    │   └── introVideo.mp4
    └── ncm/
        └── introVideo.mp4
```

## Testing Different Flavors

### Quick Test Script

Create a test screen to verify flavor configuration:

```dart
class FlavorTestScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final flavorConfig = getIt<FlavorConfig>();
    final paymentConfig = getIt<PaymentFeatureConfig>();
    final investmentConfig = getIt<InvestmentFeatureConfig>();
    final uiConfig = getIt<UiFeatureConfig>();
    
    return Scaffold(
      appBar: AppBar(title: Text('Flavor: ${flavorConfig.flavorName}')),
      body: ListView(
        children: [
          ListTile(
            title: Text('App Name'),
            subtitle: Text(flavorConfig.appName),
          ),
          ListTile(
            title: Text('Base URL'),
            subtitle: Text(flavorConfig.baseUrl),
          ),
          ListTile(
            title: Text('Payment Strategy'),
            subtitle: Text(paymentConfig.useStaticPaymentTypes ? 'Static' : 'API'),
          ),
          ListTile(
            title: Text('Investment Strategy'),
            subtitle: Text(investmentConfig.useStaticPurchasePercentage ? 'Static' : 'API'),
          ),
          ListTile(
            title: Text('Trading Wallet'),
            subtitle: Text(uiConfig.showTradingWallet ? 'Enabled' : 'Disabled'),
          ),
        ],
      ),
    );
  }
}
```

## Migration Notes

When migrating from the old AppConfig system:

1. **Flavor Detection:** The system automatically detects the flavor from build-time environment variables
2. **Backward Compatibility:** All existing feature flags are preserved
3. **Environment Handling:** Debug/production URLs are handled automatically
4. **Asset Paths:** Update asset references to use flavor-specific paths

## Customization

To customize a flavor configuration:

1. **Edit Flavor Config:** Modify the respective flavor config file
2. **Update Features:** Change payment/investment/UI configurations
3. **Test Changes:** Use the flavor test screen to verify
4. **Build & Deploy:** Use the appropriate build command

All flavors are now fully implemented and ready for use!
