# Configuration System Migration Guide

## Overview

This guide explains how to migrate from the old singleton-based configuration system to the new modular, dependency injection-based configuration architecture.

## Key Changes

### Before (Old System)
```dart
// Singleton access
final config = AppConfig.instance;
final paymentTypes = AppConfig.getAvailablePaymentTypes();
final percentages = AppConfig.getList();

// Tight coupling and hard to test
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    if (AppConfig.instance.showTradingWallet) {
      return TradingWalletWidget();
    }
    return Container();
  }
}
```

### After (New System)
```dart
// Dependency injection
final flavorConfig = getIt<FlavorConfig>();
final paymentConfig = getIt<PaymentFeatureConfig>();
final investmentConfig = getIt<InvestmentFeatureConfig>();

// Clean, testable, and modular
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final uiConfig = getIt<UiFeatureConfig>();
    if (uiConfig.showTradingWallet) {
      return TradingWalletWidget();
    }
    return Container();
  }
}
```

## Step-by-Step Migration

### 1. Update Dependency Injection Setup

Add the ConfigModule to your injectable configuration:

```dart
// In lib/core/dependency_injection/injectable.dart
@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: true,
)
void initializeGetIt() => getIt.init();
```

### 2. Replace AppConfig Usage

#### Payment Configuration
```dart
// OLD
final paymentTypes = AppConfig.getAvailablePaymentTypes(apiWalletCoins: coins);
final shouldFetch = AppConfig.shouldFetchWalletCoinsFromApi;

// NEW
final paymentConfig = getIt<PaymentFeatureConfig>();
final paymentTypes = paymentConfig.getAvailablePaymentTypes(apiWalletCoins: coins);
final shouldFetch = paymentConfig.shouldFetchWalletCoinsFromApi;
```

#### Investment Configuration
```dart
// OLD
final percentages = AppConfig.getList(apiData: data);
final shouldShow = AppConfig.shouldShowSmartInvestmentPurchasePercentage();

// NEW
final investmentConfig = getIt<InvestmentFeatureConfig>();
final percentages = investmentConfig.getPurchasePercentageList(apiData: data);
final shouldShow = investmentConfig.shouldShowPurchasePercentage();
```

#### UI Configuration
```dart
// OLD
final showWallet = AppConfig.instance.showTradingWallet;
final marketColor = AppConfig.instance.marketColor;

// NEW
final uiConfig = getIt<UiFeatureConfig>();
final showWallet = uiConfig.showTradingWallet;
final marketColor = uiConfig.marketColor;
```

#### App Metadata
```dart
// OLD
final appName = AppConfig.instance.appName;
final baseUrl = AppConfig.instance.baseUrl;

// NEW
final flavorConfig = getIt<FlavorConfig>();
final appName = flavorConfig.appName;
final baseUrl = flavorConfig.baseUrl;
```

### 3. Update BLoC/Cubit Classes

```dart
// OLD
class SmartInvestmentCubit extends Cubit<SmartInvestmentState> {
  void _updatePurchasePercentagesFromConfig() {
    final updatedPercentages = AppConfig.getList(
      apiData: state.purchasePercentageConfigData,
    );
    // ...
  }
}

// NEW
class SmartInvestmentCubit extends Cubit<SmartInvestmentState> {
  final InvestmentFeatureConfig _investmentConfig;
  
  SmartInvestmentCubit(this._investmentConfig) : super(SmartInvestmentState.initial());
  
  void _updatePurchasePercentagesFromConfig() {
    final updatedPercentages = _investmentConfig.getPurchasePercentageList(
      apiData: state.purchasePercentageConfigData,
    );
    // ...
  }
}
```

### 4. Update Screen Widgets

```dart
// OLD
class DepositScreen extends StatelessWidget {
  List<PaymentTypeModel> _buildCurrencyItems(DepositState state) {
    return AppConfig.getAvailablePaymentTypes(
      apiWalletCoins: state.walletCoins,
    );
  }
}

// NEW
class DepositScreen extends StatelessWidget {
  List<PaymentTypeModel> _buildCurrencyItems(DepositState state) {
    final paymentConfig = getIt<PaymentFeatureConfig>();
    return paymentConfig.getAvailablePaymentTypes(
      apiWalletCoins: state.walletCoins,
    );
  }
}
```

## Testing Benefits

### Before (Hard to Test)
```dart
// Difficult to mock singleton
testWidgets('should show trading wallet', (tester) async {
  // Can't easily mock AppConfig.instance
  await tester.pumpWidget(MyWidget());
  // Test depends on global state
});
```

### After (Easy to Test)
```dart
// Easy to mock with dependency injection
testWidgets('should show trading wallet', (tester) async {
  final mockUiConfig = MockUiFeatureConfig();
  when(mockUiConfig.showTradingWallet).thenReturn(true);
  
  getIt.registerSingleton<UiFeatureConfig>(mockUiConfig);
  
  await tester.pumpWidget(MyWidget());
  expect(find.byType(TradingWalletWidget), findsOneWidget);
});
```

## Adding New Flavors

### 1. Create Flavor Configuration
```dart
// lib/core/config/flavors/new_flavor_config.dart
class NewFlavorConfig {
  static FlavorConfig create() => FlavorConfig.base(
    flavor: Flavor.newFlavor,
    appName: 'New App',
    baseUrl: 'https://api.newapp.com',
    // ... other settings
    paymentConfig: PaymentFeatureConfig.api(),
    investmentConfig: InvestmentFeatureConfig.static(),
    uiConfig: UiFeatureConfig.enhanced(),
  );
}
```

### 2. Update ConfigModule
```dart
@singleton
FlavorConfig get flavorConfig {
  const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'sf');
  
  switch (flavor) {
    case 'new_flavor':
      return NewFlavorConfig.create();
    case 'sf':
    default:
      return SfFlavorConfig.create();
  }
}
```

### 3. Build with Flavor
```bash
flutter run --dart-define=FLAVOR=new_flavor
```

## Common Migration Issues

### 1. Circular Dependencies
If you get circular dependency errors, ensure configurations don't depend on each other directly.

### 2. Missing Registrations
Make sure all configurations are properly registered in ConfigModule.

### 3. Test Setup
Remember to register mock configurations in your test setup:
```dart
setUp(() {
  getIt.registerSingleton<UiFeatureConfig>(MockUiFeatureConfig());
});

tearDown(() {
  getIt.reset();
});
```

## Rollback Plan

If issues arise, you can temporarily keep both systems running:

1. Keep old AppConfig class
2. Add new configuration system alongside
3. Gradually migrate screens one by one
4. Remove old system once migration is complete

This allows for incremental migration with minimal risk.
